latitude = None
longitude = None
participiant_id = None

def update_location(lati, longi):
    print("Updating location to: ", lati, longi)
    global latitude, longitude
    latitude = lati
    longitude = longi

def update_participant(participant_id):
    global participiant_id
    participiant_id = participant_id

def get_participant():
    return participiant_id

def get_location():
    print("Getting location: ", latitude, longitude)
    if latitude is None or longitude is None:
        print("Failed to get location. Using default location.")
        return 12.878787, 77.590694
    else:
        return latitude, longitude
