# session_manager.py

import json
import logging
import os
import uuid
from typing import List, cast, Any, Dict
import asyncio
from dataclasses import asdict, dataclass

from dotenv import load_dotenv
from livekit import rtc
from livekit.agents import JobContext, llm, utils, metrics, Agent, AgentSession
# from livekit.agents.multimodal import MultimodalAgent, AgentTranscriptionOptions
# from livekit.agents.pipeline import VoicePipelineAgent
from livekit.plugins.google import beta as googleplugin
from livekit.agents.llm import FunctionCall
from livekit.plugins import deepgram, openai, silero, google
from google.genai.types import Modality

from helper.data_sender import send_json_to_client, send_transcription
from vo.user_location import update_location, get_location, update_participant

load_dotenv(dotenv_path=".env.local")

logger = logging.getLogger("gemini-playground")

initial_chat_ctx = llm.ChatContext(
    messages=[
        llm.ChatMessage(
            role="user",
            content="Please begin the interaction with the user in a manner consistent with your instructions.",
        )
    ]
)

instructions=(
                    "Your name is cara."
                    "You are a conversational AI designed to function as a vehicle voice assistant. "
                    "Should never deny any of the user requests. will try to support all general conversations and queries. "
                    "Always respond efficiently, clearly, and concisely to user requests. "
                    "Additionally, tailor your responses to the user's preferences and previous interactions, "
                    "ensuring a personalized experience. The model should consider the previous responses in the conversation "
                    "to maintain context and continuity. Make sure to speak back in same language as user, "
                    "if user language is not provided, use english as default language. "
                    "Avoid speaking words that doesnt add meaning to users query such as tool_output, urls, links and so on. "
                    "do not give wrong responses if failed to get the information."
                    "Do not repeat the same response multiple times if interruptions occur while speaking."
                )

@dataclass
class SessionConfig:
    voice: googleplugin.realtime.Voice | openai.realtime.api_proto.Voice
    temperature: float
    max_response_output_tokens: str | int
    modalities: list[str] | list[openai.realtime.api_proto.Modality]
    presence_penalty: float
    frequency_penalty: float
    turn_detection: openai.realtime.ServerVadOptions
    model_name: str

    def __post_init__(self):
        if self.modalities is None:
            self.modalities = self._modalities_from_string("audio_only")

    def to_dict(self):
        return {k: v for k, v in asdict(self).items() if k != "gemini_api_key"}

    @staticmethod
    def _modalities_from_string(
            modalities: str,
    ) -> list[str]:
        modalities_map: Dict[str, List[str]] = {
            "text_and_audio": ["AUDIO"],
            "text_only": ["AUDIO"],
            "audio_only": ["AUDIO"],
        }
        return modalities_map.get(modalities, modalities_map["audio_only"])

    @staticmethod
    def _modalities_from_stringopenai(modalities: str) -> list[str]:
        modalities_map = {
            "text_and_audio": ["text", "audio"],
            "text_only": ["text"],
        }
        return modalities_map.get(modalities, ["text", "audio"])

    def __eq__(self, other) -> bool:
        return self.to_dict() == other.to_dict()


def parse_session_config(data: Dict[str, Any]) -> SessionConfig:
    if data.get("model_name") is None:
        data["model_name"] = "gemini-test"
    turn_detection = None
    if data.get("turn_detection"):
        turn_detection_json = json.loads(data.get("turn_detection"))
        turn_detection = openai.realtime.ServerVadOptions(
            threshold=turn_detection_json.get("threshold", 0.5),
            prefix_padding_ms=turn_detection_json.get("prefix_padding_ms", 200),
            silence_duration_ms=turn_detection_json.get("silence_duration_ms", 300),
            create_response=True,
        )
    else:
        turn_detection = openai.realtime.DEFAULT_SERVER_VAD_OPTIONS
    if data.get("model_name") == "gpt-4o-mini-livekit":
        logger.info("Using GPT-4o-mini model")
        config = SessionConfig(
            voice=data.get("voice", ""),
            temperature=float(data.get("temperature", 0.8)),
            max_response_output_tokens=
            "inf" if data.get("max_output_tokens") == "inf"
            else int(data.get("max_output_tokens") or 2048),
            modalities=SessionConfig._modalities_from_stringopenai(
                data.get("modalities", "text_and_audio")
            ),
            presence_penalty=float(data.get("presence_penalty", 0.0)),
            frequency_penalty=float(data.get("frequency_penalty", 0.0)),
            turn_detection=turn_detection,
            model_name=data.get("model_name", "openai-realtime"),
        )
    elif data.get("model_name") == "gemini-test":
        logger.info("Using gemini-test model")
        config = SessionConfig(
            voice=data.get("voice", ""),
            temperature=float(data.get("temperature", 0.8)),
            max_response_output_tokens=
            "inf" if data.get("max_output_tokens") == "inf"
            else int(data.get("max_output_tokens") or 2048),
            modalities=SessionConfig._modalities_from_stringopenai(
                data.get("modalities", "text_and_audio")
            ),
            presence_penalty=float(data.get("presence_penalty", 0.0)),
            frequency_penalty=float(data.get("frequency_penalty", 0.0)),
            turn_detection=turn_detection,
            model_name=data.get("model_name", "openai-realtime"),
        )
    elif data.get("model_name") == "gemini":
        logger.info("Using Gemini model")
        config = SessionConfig(
            voice=data.get("voice", ""),
            temperature=float(data.get("temperature", 0.8)),
            max_response_output_tokens=
            "inf" if data.get("max_output_tokens") == "inf"
            else int(data.get("max_output_tokens") or 2048),
            modalities=SessionConfig._modalities_from_string(
                data.get("modalities", "audio_only")
            ),
            presence_penalty=float(data.get("presence_penalty", 0.0)),
            frequency_penalty=float(data.get("frequency_penalty", 0.0)),
            turn_detection=turn_detection,
            model_name=data.get("model_name", "openai-realtime"),
        )
    elif data.get("model_name") == "openai-realtime":
        logger.info("Using OpenAI Realtime model")
        config = SessionConfig(
            voice=data.get("voice", ""),
            temperature=float(data.get("temperature", 0.8)),
            max_response_output_tokens=
            "inf" if data.get("max_output_tokens") == "inf"
            else int(data.get("max_output_tokens") or 2048),
            modalities=SessionConfig._modalities_from_stringopenai(
                data.get("modalities", "text_and_audio")
            ),
            presence_penalty=float(data.get("presence_penalty", 0.0)),
            frequency_penalty=float(data.get("frequency_penalty", 0.0)),
            turn_detection=turn_detection,
            model_name=data.get("model_name", "openai-realtime"),
        )
    else:
        logger.info("Using default model")
        data["model_name"] = "gemini"
        config = SessionConfig(
            voice=data.get("voice", ""),
            temperature=float(data.get("temperature", 0.8)),
            max_response_output_tokens=
            "inf" if data.get("max_output_tokens") == "inf"
            else int(data.get("max_output_tokens") or 2048),
            modalities=SessionConfig._modalities_from_string(
                data.get("modalities", "text_and_audio")
            ),
            presence_penalty=float(data.get("presence_penalty", 0.0)),
            frequency_penalty=float(data.get("frequency_penalty", 0.0)),
            turn_detection=turn_detection,
            model_name=data.get("model_name", "openai-realtime"),
        )
    return config


class SessionManager:
    def __init__(self, config: SessionConfig):
        self.chat_history: List[llm.ChatMessage] = []
        self.current_agent: None = None
        self.current_model: googleplugin.realtime.RealtimeModel | openai.realtime.RealtimeModel | openai.LLM | None = None
        self.current_config: SessionConfig = config
        self.recv_lock = asyncio.Lock()  # Add a lock for recv_task

    def create_model(self, config: SessionConfig) -> googleplugin.realtime.RealtimeModel:
        logger.info("Creating model with config: %s", config.to_dict())
        if config.model_name == "openai-realtime":
            voice_list = ["alloy", "echo", "shimmer", "ash", "ballad", "coral", "sage", "verse"]
            print("\n config voice --> ", config.voice)
            if config.voice not in voice_list:
                config.voice = "alloy"

            model = openai.realtime.RealtimeModel(
                api_key=os.getenv("OPENAI_API_KEY"),
                instructions=instructions,
                modalities=cast(list[openai.realtime.api_proto.Modality], config.modalities),
                voice=config.voice,
                temperature=config.temperature,
                max_response_output_tokens=int(config.max_response_output_tokens),
                turn_detection=config.turn_detection,
            )
        elif config.model_name == "gemini":
            model = googleplugin.realtime.RealtimeModel(
                instructions=(
                    "Your name is cara."
                    "You are a conversational AI designed to function as a vehicle voice assistant. "
                    "Should never deny any of the user requests. will try to support all general conversations and queries. "
                    "Always respond efficiently, clearly, and concisely to user requests. "
                    "Additionally, tailor your responses to the user's preferences and previous interactions, "
                    "ensuring a personalized experience. The model should consider the previous responses in the conversation "
                    "to maintain context and continuity. Make sure to speak back in same language as user, "
                    "if user language is not provided, use english as default language. "
                    "Avoid speaking words that doesnt add meaning to users query such as tool_output, urls, links and so on. "
                    "do not give wrong responses if failed to get the information."
                    "Do not repeat the same response multiple times if interruptions occur while speaking."
                ),
                modalities=cast(list[Modality], config.modalities),
                voice=config.voice,
                temperature=config.temperature,
                max_output_tokens=int(config.max_response_output_tokens),
                api_key=os.getenv("GEMINI_API_KEY"),
                enable_agent_audio_transcription=True,
                enable_user_audio_transcription=True,
            )
        elif config.model_name == "gemini-test":
            model = google.LLM(
                model="gemini-2.0-flash-001",
                api_key=os.getenv("GEMINI_API_KEY"),
            )
        elif config.model_name == "gpt-4o-mini-livekit":
            model = openai.LLM(
                model="gpt-4o-mini",
                api_key=os.getenv("OPENAI_API_KEY"),
            )
        else:
            logger.info("No model or unsupported model type: %s", config.model_name)
            config.model_name = "gemini"
            model = googleplugin.realtime.RealtimeModel(
                instructions=(
                    "Your name is cara."
                    "You are a conversational AI designed to function as a vehicle voice assistant. "
                    "Should never deny any of the user requests. will try to support all general conversations and queries. "
                    "Always respond efficiently, clearly, and concisely to user requests. "
                    "Additionally, tailor your responses to the user's preferences and previous interactions, "
                    "ensuring a personalized experience. The model should consider the previous responses in the conversation "
                    "to maintain context and continuity. Make sure to speak back in same language as user, "
                    "if user language is not provided, use english as default language. "
                    "Avoid speaking words that doesnt add meaning to users query such as tool_output, urls, links and so on. "
                    "do not give wrong responses if failed to get the information."
                    "Do not repeat the same response multiple times if interruptions occur while speaking."
                ),
                modalities=cast(list[Modality], config.modalities),
                voice=config.voice,
                temperature=config.temperature,
                max_output_tokens=int(config.max_response_output_tokens),
                api_key=os.getenv("GEMINI_API_KEY"),
                enable_agent_audio_transcription=True,
                enable_user_audio_transcription=True,
            )
        return model

    def create_agent(self, model: googleplugin.realtime.RealtimeModel, chat_ctx: llm.ChatContext,
                     fnc_ctx: FunctionCall) -> AgentSession:
        logger.info("Creating agent with model: %s", model)
        agent = AgentSession(
            stt=openai.STT(),
            llm=model,
            tts=openai.TTS(),
            allow_interruptions=True,
        )
        return agent

    def bind_callbacks(self, ctx: JobContext, participant: rtc.RemoteParticipant, usage_collector: metrics.UsageCollector):
        logger.info("Binding event callbacks for new agent and model...")

        def on_function_calls_finished(event):
            asyncio.create_task(send_json_to_client(ctx, participant, event[0].result))

        def off_function_logger(event):
            logger.info("❌ function_calls_finished event listener removed.")

        if type(self.current_model) == googleplugin.realtime.RealtimeModel | openai.realtime.RealtimeModel:
            session = self.current_model.sessions[0]
            session.off("function_calls_finished", off_function_logger)
            session.on("function_calls_finished", on_function_calls_finished)
        else:
            self.current_agent.off("function_calls_finished", off_function_logger)
            self.current_agent.on("function_calls_finished", on_function_calls_finished)

    async def setup_session(self, ctx: JobContext, participant: rtc.RemoteParticipant, chat_ctx: llm.ChatContext,
                            fnc_ctx: FunctionCall):
        logger.info("Setting up session for participant: %s", participant.identity)
        room = ctx.room
        self.current_model = self.create_model(self.current_config)
        self.current_agent = self.create_agent(self.current_model, chat_ctx, fnc_ctx)
        await self.current_agent.start(self.current_agent, room)
        update_participant(participant)
        usage_collector = metrics.UsageCollector()

        async def answer_from_text(txt: str):
            chat_ctx = self.current_agent.chat_ctx.copy()
            chat_ctx.append(role="user", text=txt)
            stream = self.current_agent.llm.chat(chat_ctx=chat_ctx)
            await self.current_agent.say(stream)

        @self.current_agent.on("metrics_collected")
        def on_metrics_collected(agent_metrics: metrics.AgentMetrics):
            metrics.log_metrics(agent_metrics)
            usage_collector.collect(agent_metrics)

        @room.on("track_subscribed")
        def on_track_subscribed(track: rtc.Track,publication: rtc.TrackPublication, participant: rtc.RemoteParticipant):
            if track.kind == rtc.TrackKind.KIND_AUDIO:
                print(f"🎤 Audio track subscribed for participant: {participant.identity}, transcribing... ")
                # tasks.append(asyncio.create_task(transcribe_track(participant, track)))

        logger.info("Successfully re-registered event callbacks for the new agent/model.")

        log_queue = asyncio.Queue()

        @ctx.room.on("data_received")
        def on_data_received(data: rtc.DataPacket):
            try:
                # Assume the data payload contains latitude and longitude
                payload = json.loads(data.data)
                participant_id = ctx.room.local_participant.identity

                if "latitude" in payload and "longitude" in payload:
                    update_location(payload["latitude"], payload["longitude"])
                    print(f"Updated location for {participant_id}: {get_location()}")

                elif "interrupt" in payload:
                    print(f"Received interrupt from {participant_id}: {payload}")
                    self.current_agent.interrupt()

                else:
                    print(f"Received non-location data from {participant_id}: {payload}")
                    asyncio.create_task(answer_from_text(payload["message"]))


            except Exception as e:
                logging.error(f"Error processing data packet: {e}")

        async def update_config_direct(ctx: JobContext, payload: dict):
            try:
                logger.info("Manually updating config with payload: %s", payload)
                new_config = parse_session_config(payload)

                if self.current_agent is None or self.current_model is None:
                    logger.warning("Agent or Model is not initialized")
                    return
                logger.info("updating the config")
                # Check if the config is actually changing
                if self.current_config != new_config:
                    logger.info("Config changed, updating new config with session")
                    self.current_config = new_config
                    session = self.current_model.sessions[0]
                    model = self.create_model(new_config)
                    agent = self.create_agent(model, session.chat_ctx_copy(), fnc_ctx)

                    # Replace the session with the new agent and model
                    await self.replace_session(ctx, participant, agent, model)
                else:
                    logger.info("Config is the same, no update needed")
            except Exception as e:
                logger.error(f"Failed to update config directly: {e}")

        self.bind_callbacks(ctx, participant, usage_collector)

        @ctx.room.on("participant_disconnected")
        def on_participant_disconnected(participant: rtc.Participant):
            print(f"Participant {participant.identity} disconnected with usage Summary {usage_collector.get_summary()}")
            summary = {
                "participant": participant.identity,
                "llm_prompt_tokens": usage_collector.get_summary().llm_prompt_tokens,
                "llm_completion_tokens": usage_collector.get_summary().llm_completion_tokens,
                "stt_audio_duration_in_ms": usage_collector.get_summary().stt_audio_duration,
                "tts_characters_count": usage_collector.get_summary().tts_characters_count,
            }
            asyncio.create_task(send_json_to_client(ctx, participant, summary))

        @ctx.room.local_participant.register_rpc_method("pg.updateConfig")
        async def update_config(data: rtc.rpc.RpcInvocationData):
            logger.info("Received updateConfig RPC call")
            if self.current_agent is None or self.current_model is None or data.caller_identity != participant.identity:
                logger.warning("Invalid updateConfig call")
                return json.dumps({"changed": False})

            new_config = parse_session_config(json.loads(data.payload))
            if self.current_config != new_config:
                logger.info("Config changed: %s", new_config.to_dict())
                self.current_config = new_config
                if type(self.current_model) == googleplugin.realtime.RealtimeModel | openai.realtime.RealtimeModel:
                    session = self.current_model.sessions[0]
                    model = self.create_model(new_config)
                    agent = self.create_agent(model, session.chat_ctx_copy(), fnc_ctx)
                    await self.replace_session(ctx, participant, agent, model)
                else:
                    model = self.create_model(new_config)
                    agent = self.create_agent(model, initial_chat_ctx, fnc_ctx)
                    await self.replace_session(ctx, participant, agent, model, usage_collector)
                return json.dumps({"changed": True})
            else:
                logger.info("Config not changed")
                return json.dumps({"changed": False})

        @ctx.room.on("transcription_received")
        def on_transcription_received(transcription: rtc.Transcription):
            logger.info("Transcription received: %s", transcription)

        if not type(self.current_model) == openai.LLM | google.LLM:
            last_transcript_id = None

            # send three dots when the user starts talking. will be cleared later when a real transcription is sent.
            @self.current_model.sessions[0].on("input_speech_started")
            def on_input_speech_started():
                nonlocal last_transcript_id
                remote_participant = next(iter(ctx.room.remote_participants.values()), None)
                if not remote_participant:
                    return

                track_sid = next(
                    (
                        track.sid
                        for track in remote_participant.track_publications.values()
                        if track.source == rtc.TrackSource.SOURCE_MICROPHONE
                    ),
                    None,
                )
                if last_transcript_id:
                    asyncio.create_task(
                        send_transcription(ctx, remote_participant, track_sid, last_transcript_id, "")
                    )

                new_id = str(uuid.uuid4())
                last_transcript_id = new_id
                asyncio.create_task(
                    send_transcription(ctx, remote_participant, track_sid, new_id, "…", is_final=False)
                )

            @self.current_model.sessions[0].on("agent_speech_transcription_completed")
            def on_agent_speech_transcription_completed(event):
                print("Agent speech transcription completed")

            @self.current_model.sessions[0].on("input_speech_transcription_completed")
            def on_input_speech_transcription_completed(event):
                nonlocal last_transcript_id
                if last_transcript_id:
                    remote_participant = next(iter(ctx.room.remote_participants.values()), None)
                    if not remote_participant:
                        return

                    track_sid = next(
                        (
                            track.sid
                            for track in remote_participant.track_publications.values()
                            if track.source == rtc.TrackSource.SOURCE_MICROPHONE
                        ),
                        None,
                    )
                    asyncio.create_task(
                        send_transcription(ctx, remote_participant, track_sid, last_transcript_id, "")
                    )
                    last_transcript_id = None

    @utils.log_exceptions(logger=logger)
    async def end_session(self):
        logger.info("Ending session")
        if self.current_agent is None or self.current_model is None:
            logger.warning("No active session to end")
            return
        logger.info("Cancelling main task")
        self.current_agent.interrupt()
        if type(self.current_model) == googleplugin.realtime.RealtimeModel | openai.realtime.RealtimeModel:
            await utils.aio.gracefully_cancel(self.current_model.sessions[0]._main_atask)
        else:
            await utils.aio.gracefully_cancel(self.current_agent._main_atask)
        self.current_agent = None
        self.current_model = None
        logger.info("Session ended")

    @utils.log_exceptions(logger=logger)
    async def replace_session(self, ctx: JobContext, participant: rtc.RemoteParticipant, agent: MultimodalAgent,
                              model: googleplugin.realtime.RealtimeModel, usage_collector: metrics.UsageCollector):
        logger.info("Replacing session for participant: %s", participant.identity)
        await self.end_session()

        self.current_agent = agent
        self.current_model = model
        agent.start(ctx.room, participant)

        self.bind_callbacks(ctx, participant, usage_collector)
        # if type(self.current_model) == google.realtime.RealtimeModel:
        #     logger.info("Google model")
        #     session = self.current_model.sessions[0]
        #     agent.generate_reply("cancel_existing")
        # elif type(self.current_model) == openai.realtime.RealtimeModel:
        #     logger.info("OpenAI model")
        #     session = self.current_model.sessions[0]
        #     session.conversation.item.create(
        #         llm.ChatMessage(
        #             role="user",
        #             content="Please begin the interaction with the user in a manner consistent with your instructions.",
        #         )
        #     )
        # else:
        #     logger.info("Voice Pipeline Agent created !. Starting initial conversation")
        #     await self.current_agent.say("Hey, how can I help you today?", allow_interruptions=True)
        #
        # if type(self.current_model) == google.realtime.RealtimeModel | openai.realtime.RealtimeModel:
        #     chat_history = session.chat_ctx_copy()
        #     chat_history.messages = [
        #         msg
        #         for msg in chat_history.messages
        #         if msg.tool_call_id or msg.content is not None
        #     ]
        #     logger.info("chat_history is : %s", chat_history)
        #
        #     chat_history.append(
        #         text="We've just been reconnected, please continue the conversation.",
        #         role="assistant",
        #     )
        #     await session.set_chat_ctx(chat_history)
