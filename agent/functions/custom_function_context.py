import os
import json
import logging
import uuid
import requests

from pydantic import Field

from datetime import datetime, timedelta
from dateutil import parser
from typing import Annotated, ClassVar, Optional
from livekit.agents import llm
from livekit.agents.llm import function_tool, FunctionCall

from utils.location import getAddress, getCoords
from utils.spotify import fetch_songs_by_artist, search_song_on_spotify, search_tracks_on_spotify
from utils.memory_db import save_in_memory_db
from utils.weather import fetch_weather_data, format_weather_response
from vo.user_location import get_location
from vo.user_location import get_participant

class CustomFunctionContext(FunctionCall):
    global_context: ClassVar[Optional[object]] = None  # Class-level attribute for global context

    @staticmethod
    def set_context(context):
        """Sets the global context."""
        CustomFunctionContext.global_context = context

    @classmethod
    def get_context(cls):
        """Gets the global context."""
        if cls.global_context is None:
            raise ValueError("Global context has not been set.")
        return cls.global_context

    @function_tool(
        name="assistant_info",
        description="Provides information about the AI assistant, including its name, purpose, and functionality. Responds in the same language as the user."
    )
    def assistant_info(
            self,
            query: Annotated[str, Field(description="The user's question about the assistant.")]
    ) -> str:
        """
        Responds to questions about the AI assistant, 'Cara'.
        """
        assistant_description = (
            "My name is Cara. I am a conversational AI designed to function as a vehicle voice assistant. "
            "I respond efficiently, clearly, and concisely to user requests while maintaining context and continuity. "
            "I personalize responses based on your preferences and previous interactions. "
            "I can assist you with navigation, music, reminders, weather updates, and more. "
            "I also communicate in the same language as you, using English by default."
        )

        return json.dumps({
            "type": "assistant_info",
            "assistant": {
                "message": assistant_description
            }
        })

    @function_tool(
        name="get_user_locale_time",
        description="Retrieves the user's current locale, local time, date and so on. time changes every second so to be called every time user asks for time."
    )
    async def get_user_locale_time(self) -> str:
        """Performs an RPC call to get the user's locale and returns the current local time."""
        print("get_user_locale_time")
        try:
            # Perform RPC call to get user's locale
            context = self.get_context()
            rpc_result = await context.room.local_participant.perform_rpc(
                destination_identity=get_participant().identity,
                method='rpc_by_client',  # Replace with actual RPC method if needed
                payload=json.dumps({
                    "type": "time_request"
                })
            )

            # Parse response
            result_dict = json.loads(rpc_result)
            locale = result_dict.get("locale", "en-US")  # Default to English if missing
            timezone = result_dict.get("timezone", "UTC")  # Default to UTC
            current_time = result_dict.get("currentTime", "00:00:00")
            country = result_dict.get("country", "Unknown")
            language = result_dict.get("language", "English")

            # Construct response
            response = {
                "type": "user_locale_time",
                "assistant": {
                    "message": f"Your current locale is {locale}, and the local time is {current_time}.",
                    "locale": locale,
                    "timezone": timezone,
                    "current_time": current_time,
                    "country": country,
                    "language": language
                }
            }

            return json.dumps(response)

        except Exception as e:
            return json.dumps({
                "type": "user_locale_time",
                "assistant": {
                    "message": f"Error fetching user locale and time: {str(e)}",
                    "locale": "unknown",
                    "timezone": "unknown",
                    "current_time": "unknown"
                }
            })

    @function_tool(
        name="current_weather",
        description="Fetches the current weather data(temperature, humidity, rain, wind speed, sunrise and sunset time and so on) for a specified location or the user's current location if user doesn't specify the location."
    )
    async def current_weather(
            self,
            location: Annotated[str, Field(
                description="Location for which weather is requested. If not provided, fetch current location weather.")] = None,
    ) -> str:
        """Fetches current weather data for a specified or user's location."""
        try:
            API_KEY = os.environ.get("OPENWEATHER_API_KEY")

            if location:
                latitude, longitude , city = getCoords(location)
                print(f"latitude: {latitude}, longitude: {longitude}")
                url = f"https://api.openweathermap.org/data/3.0/onecall?lat={latitude}&lon={longitude}&appid={API_KEY}&units=metric"
            elif location is None:
                context = self.get_context()
                payload = json.dumps({
                    "type": "location_request",
                    "highAccuracy": True
                })
                response = await context.room.local_participant.perform_rpc(
                    destination_identity=get_participant().identity,
                    method='rpc_by_client',
                    payload=payload
                )
                print(f"Call result: {response}")
                latitude, longitude = json.loads(response).get("latitude"), json.loads(response).get("longitude")
                if latitude and longitude is None:
                    latitude, longitude = get_location()

                url = f"https://api.openweathermap.org/data/3.0/onecall?lat={latitude}&lon={longitude}&appid={API_KEY}&units=metric"
                city = getAddress(latitude, longitude)
                print(f"url is: {url}")
                parts = city.split(",")
                if len(parts)>=3:
                    city = parts[-3].strip()
                print(f"context: {city}")
            else:
                return json.dumps({"type": "weather", "assistant": {"message": "Location data unavailable."}})

            weather_data = await fetch_weather_data(url)
            return format_weather_response(weather_data, city)
        except Exception as e:
            return json.dumps({"type": "weather", "assistant": {"message": f"Error: {str(e)}"}})

    @function_tool(
        name="past_weather",
        description="Fetches past weather data(temperature, humidity, rain, wind speed, sunrise and sunset time and so on) for a specified date and location or the user's current location. should speak in past tense since its weather history."
    )
    async def past_weather(
            self,
            location: Annotated[str, Field(description="Location for past weather data.")] = None,
            date: Annotated[str, Field(description="Date in YYYY-MM-DD format or relative date like 'yesterday'.")] = None,
    ) -> str:
        """Fetches past weather data for a given location and date."""
        try:
            API_KEY = os.environ.get("OPENWEATHER_API_KEY")

            # Convert relative date inputs into a proper date
            if date.lower() == "yesterday":
                date = (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d")
            elif "days ago" in date:
                days_ago = int(date.split()[0])  # Extract number of days
                date = (datetime.now() - timedelta(days=days_ago)).strftime("%Y-%m-%d")
            elif date.lower() == "last week":
                date = (datetime.now() - timedelta(days=7)).strftime("%Y-%m-%d")
            elif date.lower() == "last month":
                date = (datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d")

            # Convert date to timestamp
            timestamp = int(datetime.strptime(date, "%Y-%m-%d").timestamp())

            if location:
                latitude, longitude, city = getCoords(location)
                print(f"latitude: {latitude}, longitude: {longitude}")
                url = f"https://api.openweathermap.org/data/3.0/onecall/timemachine?lat={latitude}&lon={longitude}&dt={timestamp}&appid={API_KEY}&units=metric"
            elif location is None:
                context = self.get_context()
                payload = json.dumps({
                    "type": "location_request",
                    "highAccuracy": True
                })
                response = await context.room.local_participant.perform_rpc(
                    destination_identity=get_participant().identity,
                    method='rpc_by_client',
                    payload=payload
                )
                print(f"Call result for past_weather: {response}")
                latitude, longitude = json.loads(response).get("latitude"), json.loads(response).get("longitude")
                if latitude and longitude is None:
                    latitude, longitude = get_location()
                url = f"https://api.openweathermap.org/data/3.0/onecall/timemachine?lat={latitude}&lon={longitude}&dt={timestamp}&appid={API_KEY}&units=metric"
                city = getAddress(latitude, longitude)
                parts = city.split(",")
                if len(parts) >= 3:
                    city = parts[-3].strip()
                print(f"context: {city}")
            else:
                return json.dumps({"type": "weather", "assistant": {"message": "Location data unavailable."}})

            weather_data = await fetch_weather_data(url)
            return format_weather_response(weather_data, city)

        except ValueError as e:
            return json.dumps({"type": "weather", "assistant": {"message": f"Invalid date format: {str(e)}"}})
        except Exception as e:
            return json.dumps({"type": "weather", "assistant": {"message": f"Error: {str(e)}"}})

    @function_tool(
        name="future_weather",
        description="Fetches the weather forecast for the next few days for a given location or the user's current location."
    )
    async def future_weather(
            self,
            location: Annotated[str, Field(description="Location for future weather forecast.")] = None,
            days: Annotated[int, Field(description="Number of days ahead for the forecast (1-8).")] = 1,
    ) -> str:
        """Fetches future weather forecast for a specified location."""
        try:
            if days < 1 or days > 8:
                return json.dumps(
                    {"type": "weather_forecast", "assistant": {"message": "Forecast available for 1 to 8 days only."}})

            API_KEY = os.environ.get("OPENWEATHER_API_KEY")

            if location:
                latitude, longitude, city = getCoords(location)
                print(f"latitude: {latitude}, longitude: {longitude}")
                url = f"https://api.openweathermap.org/data/3.0/onecall?lat={latitude}&lon={longitude}&exclude=minutely,hourly,current,alerts&appid={API_KEY}&units=metric"
            elif location is None:
                context = self.get_context()
                payload = json.dumps({
                    "type": "location_request",
                    "highAccuracy": True
                })
                response = await context.room.local_participant.perform_rpc(
                    destination_identity=get_participant().identity,
                    method='rpc_by_client',
                    payload=payload
                )
                print(f"Call result for future weather: {response}")
                latitude, longitude = json.loads(response).get("latitude"), json.loads(response).get("longitude")
                if latitude and longitude is None:
                    latitude, longitude = get_location()
                url = f"https://api.openweathermap.org/data/3.0/onecall?lat={latitude}&lon={longitude}&exclude=minutely,hourly,current,alerts&appid={API_KEY}&units=metric"
                city = getAddress(latitude, longitude)
                parts = city.split(",")
                if len(parts) >= 3:
                    city = parts[-3].strip()
                print(f"context: {city}")
            else:
                return json.dumps({"type": "weather_forecast", "assistant": {"message": "Location data unavailable."}})

            weather_data = await fetch_weather_data(url)
            return format_weather_response(weather_data, city, is_future=True)

        except Exception as e:
            return json.dumps({"type": "weather_forecast", "assistant": {"message": f"Error: {str(e)}"}})

    @function_tool(
        name="rain_forecast",
        description="Fetches the probability of rain for a specified date and location or user's current location and time."
    )
    async def rain_forecast(
            self,
            location: Annotated[str, Field(description="Location for rain forecast.")] = None,
            date: Annotated[
                str, Field(description="Date in YYYY-MM-DD format or relative date like 'tomorrow'.")] = None,
    ) -> str:
        """Fetches rain probability for a given location and date."""
        print("rain_forecast")
        try:
            API_KEY = os.environ.get("OPENWEATHER_API_KEY")
            # Convert relative date inputs into a proper date
            if date.lower() == "today":
                date = datetime.utcnow().strftime("%Y-%m-%d")
            elif date.lower() == "tomorrow":
                date = (datetime.utcnow() + timedelta(days=1)).strftime("%Y-%m-%d")

            if location:
                latitude, longitude, city = getCoords(location)
                print(f"latitude: {latitude}, longitude: {longitude}")
                url = f"https://api.openweathermap.org/data/3.0/onecall?lat={latitude}&lon={longitude}&exclude=current,minutely,hourly,alerts&appid={API_KEY}&units=metric"
            elif location is None:
                print("location is none")
                context = self.get_context()
                payload = json.dumps({
                    "type": "location_request",
                    "highAccuracy": True
                })
                response = await context.room.local_participant.perform_rpc(
                    destination_identity=get_participant().identity,
                    method='rpc_by_client',
                    payload=payload
                )
                print(f"Call result for rain forecast: {response}")
                latitude, longitude = json.loads(response).get("latitude"), json.loads(response).get("longitude")
                if latitude and longitude is None:
                    latitude, longitude = get_location()
                url = f"https://api.openweathermap.org/data/3.0/onecall?lat={latitude}&lon={longitude}&exclude=current,minutely,hourly,alerts&appid={API_KEY}&units=metric"
                location = getAddress(latitude, longitude)
                parts = location.split(",")
                if len(parts) >= 3:
                    location = parts[-3].strip()
                print(f"context: {location}")
            else:
                return json.dumps({"type": "weather", "assistant": {"message": "Location data unavailable."}})

            weather_data = await fetch_weather_data(url)

            # Find the forecast for the requested date
            for day in weather_data["daily"]:
                forecast_date = datetime.utcfromtimestamp(day["dt"]).strftime("%Y-%m-%d")
                if forecast_date == date:
                    rain_probability = day.get("pop", 0) * 100  # Convert to percentage
                    message = f"The probability of rain in {location or 'your area'} on {date} is {rain_probability}%."
                    return json.dumps({"type": "weather", "assistant": {"message": message}})

            return json.dumps(
                {"type": "weather", "assistant": {"message": "No rain forecast available for this date."}})

        except Exception as e:
            return json.dumps({"type": "weather", "assistant": {"message": f"Error: {str(e)}"}})

    @function_tool(
        name="uv_index",
        description="Fetches the UV index for a specified location and date if not mentioned fetches for current location and time."
    )
    async def uv_index(
            self,
            location: Annotated[str, Field(description="Location for UV index.")] = None,
            date: Annotated[str, Field(description="Date in YYYY-MM-DD format. Can also be 'today' or 'tomorrow'.")] = None,
    ) -> str:
        """Fetches UV index for a given location and date."""
        try:
            API_KEY = os.environ.get("OPENWEATHER_API_KEY")

            # Convert relative date inputs
            if date.lower() == "today":
                date = datetime.utcnow().strftime("%Y-%m-%d")
            elif date.lower() == "tomorrow":
                date = (datetime.utcnow() + timedelta(days=1)).strftime("%Y-%m-%d")

            if location:
                latitude, longitude, city = getCoords(location)
                print(f"latitude: {latitude}, longitude: {longitude}")
                url = f"https://api.openweathermap.org/data/3.0/onecall?lat={latitude}&lon={longitude}&exclude=current,minutely,hourly,alerts&appid={API_KEY}&units=metric"
            elif location is None:
                context = self.get_context()
                payload = json.dumps({
                    "type": "location_request",
                    "highAccuracy": True
                })
                response = await context.room.local_participant.perform_rpc(
                    destination_identity=get_participant().identity,
                    method='rpc_by_client',
                    payload=payload
                )
                print(f"Call result for uv index: {response}")
                latitude, longitude = json.loads(response).get("latitude"), json.loads(response).get("longitude")
                if latitude and longitude is None:
                    latitude, longitude = get_location()
                url = f"https://api.openweathermap.org/data/3.0/onecall?lat={latitude}&lon={longitude}&exclude=current,minutely,hourly,alerts&appid={API_KEY}&units=metric"
                location = getAddress(latitude, longitude)
                parts = location.split(",")
                if len(parts) >= 3:
                    location = parts[-3].strip()
                print(f"context: {location}")
            else:
                return json.dumps({"type": "weather", "assistant": {"message": "Location data unavailable."}})

            weather_data = await fetch_weather_data(url)

            # Find the forecast for the requested date
            for day in weather_data["daily"]:
                forecast_date = datetime.utcfromtimestamp(day["dt"]).strftime("%Y-%m-%d")
                if forecast_date == date:
                    uv_index = day.get("uvi", "N/A")
                    message = f"The UV index in {location or 'your area'} on {date} is {uv_index}."
                    return json.dumps({"type": "weather", "assistant": {"message": message}})

            return json.dumps(
                {"type": "weather", "assistant": {"message": "UV index data not available for this date."}})

        except Exception as e:
            return json.dumps({"type": "weather", "assistant": {"message": f"Error: {str(e)}"}})

    @function_tool(
        name="weather_alerts",
        description="Fetches weather alerts for a specified location if not mentioned fetches for current location"
    )
    async def weather_alerts(
            self,
            location: Annotated[str, Field(description="Location for weather alerts.")] = None,
    ) -> str:
        """Fetches weather alerts for a given location."""
        try:
            API_KEY = os.environ.get("OPENWEATHER_API_KEY")

            if location:
                latitude, longitude, city = getCoords(location)
                print(f"latitude: {latitude}, longitude: {longitude}")
                url = f"https://api.openweathermap.org/data/3.0/onecall?lat={latitude}&lon={longitude}&appid={API_KEY}&units=metric"
            elif location is None:
                context = self.get_context()
                payload = json.dumps({
                    "type": "location_request",
                    "highAccuracy": True
                })
                response = await context.room.local_participant.perform_rpc(
                    destination_identity=get_participant().identity,
                    method='rpc_by_client',
                    payload=payload
                )
                print(f"Call result for weather alerts: {response}")
                latitude, longitude = json.loads(response).get("latitude"), json.loads(response).get("longitude")
                if latitude and longitude is None:
                    latitude, longitude = get_location()
                url = f"https://api.openweathermap.org/data/3.0/onecall?lat={latitude}&lon={longitude}&appid={API_KEY}&units=metric"
                location = getAddress(latitude, longitude)
                parts = location.split(",")
                if len(parts) >= 3:
                    location = parts[-3].strip()
                print(f"context: {location}")
            else:
                return json.dumps({"type": "weather", "assistant": {"message": "Location data unavailable."}})

            weather_data = await fetch_weather_data(url)
            alerts = weather_data.get("alerts", [])

            if not alerts:
                return json.dumps({"type": "weather", "assistant": {"message": "No weather alerts for this location."}})

            alert_messages = [f"{alert['event']}: {alert['description']}" for alert in alerts]
            return json.dumps({"type": "weather", "assistant": {"message": "\n".join(alert_messages)}})

        except Exception as e:
            return json.dumps({"type": "weather", "assistant": {"message": f"Error: {str(e)}"}})

    @function_tool(
        name="play_specific_song",
        description="Plays a specific song requested by the user and confirms the action. Only speak the song name and artist name if available and do not speak out any urls such as https://spotify.com..."
    )
    def play_specific_song(
            self,
            song: Annotated[str, Field(description="The name of the song to play")],
            artist: Annotated[str, Field(description="The artist of the song")] = None
    ) -> str:
        """Plays a requested song using Spotify."""

        # Fetch song details from Spotify
        song_details = search_song_on_spotify(song, artist)

        if not song_details:
            message = f"Sorry, I couldn't find '{song}' on Spotify."
            result = {
                "type": "music",
                "assistant": {
                    "message": message,
                    "songs": []
                }
            }
        else:
            message = f"Playing '{song_details['song_name']}' by {song_details['artists']} now. Enjoy your music!"
            result = {
                "type": "music",
                "assistant": {
                    "message": message,
                    "songs": [song_details]
                }
            }

        return json.dumps(result)

    @function_tool(
        name="play_artist_songs",
        description="Plays songs from a requested artist. Only speak the names of the songs from the list and do not speak out any urls such as https://spotify.com..."
    )
    def play_artist_songs(
            self,
            artist: Annotated[str, Field(description="The name of the artist whose songs should be played")]
    ) -> str:
        """Fetches and plays songs dynamically from the requested artist."""
        recommended_songs = fetch_songs_by_artist(artist)

        if not recommended_songs:
            message = f"here are some songs by {artist}. which one would you like to play now!"
            recommended_songs = self.search_tracks()
        else:
            message = f"here are some songs by {artist}. which one would you like to play now!"

        result = {
            "type": "music",
            "assistant": {
                "message": message,
                "songs": recommended_songs
            }
        }
        return json.dumps(result)

    @function_tool(
        name="search_tracks",
        description="Searches for a list of tracks based on user query and list the song suggestions to user. Only speak the names of the songs from the list and artists name if available and do not speak out any urls such as https://spotify.com...."
    )
    def search_tracks(
            self,
            query: Annotated[str, Field(description="The track keyword to search for (e.g., 'Hindi', 'Workout', 'Love').")]
    ) -> str:
        """
        Uses the Spotify Search API to find tracks based on user input.
        """

        if not query:
            return json.dumps({
                "type": "music",
                "assistant": {
                    "message": "Please provide a keyword to search for tracks.",
                    "tracks": []
                }
            })

        query = query.strip()
        print(f"Searching tracks for '{query}'...")

        tracks = search_tracks_on_spotify(query)

        if not tracks:
            return json.dumps({
                "type": "music",
                "assistant": {
                    "message": f"Sorry, I couldn't find any tracks for '{query}'.",
                    "tracks": []
                }
            })

        return json.dumps({
            "type": "music",
            "assistant": {
                "message": f"Here are some tracks suggestions for '{query}': which one would you like to play?",
                "tracks": tracks
            }
        })

    @function_tool(
        name="time_based_reminder",
        description="Sets a time-based reminder using the user's local time retrieved via RPC."
    )
    async def time_based_reminder(
            self,
            task: Annotated[str,Field(description="The task or reminder details provided by the user."),],
            trigger_time: Annotated[str,Field(description="The exact time to trigger the reminder, in the format '%Y-%m-%dT%H:%M:%S'.")] = None,
            delay_in_minutes: Annotated[int,Field(description="The delay in minutes before triggering the reminder.")] = None,
    ) -> str:


        # **Step 1: Get User's Current Time via RPC**
        try:
            context = self.get_context()
            rpc_result = await context.room.local_participant.perform_rpc(
                destination_identity=get_participant().identity,
                method="rpc_by_client",  # Ensure this method is implemented on the client
                payload=json.dumps({"type": "time_request"}),
            )

            # **Step 2: Parse RPC Response**
            result_dict = json.loads(rpc_result)
            client_current_time_str = result_dict.get("currentTime", None)  # Get user’s local time

            if not client_current_time_str:
                raise ValueError("No valid currentTime received from RPC.")

            # **Step 3: Handle ISO 8601 format with Timezone Offset**
            try:
                client_current_time = parser.isoparse(client_current_time_str)
                print(f"🔹 Parsed client time: {client_current_time.isoformat()}")
            except ValueError:
                raise ValueError(f"Unrecognized time format received: {client_current_time_str}")

        except Exception as e:
            print(f"❌ Failed to get client time via RPC: {str(e)}")
            return json.dumps({
                "type": "reminder",
                "assistant": {
                    "status": "error",
                    "message": "Failed to retrieve your local time. Please try again later.",
                    "error": str(e),
                }
            })

        # **Step 4: Validate Reminder Time**
        if not trigger_time and not delay_in_minutes:
            return json.dumps({
                "type": "reminder",
                "assistant": {
                    "status": "error",
                    "message": "Please provide either a trigger time or a delay in minutes.",
                    "error": "Missing trigger_time and delay_in_minutes",
                }
            })

        if trigger_time:
            try:
                trigger_time_dt = parser.isoparse(trigger_time)  # Automatically handles timezone offset
            except ValueError:
                return json.dumps({
                    "type": "reminder",
                    "assistant": {
                        "status": "error",
                        "message": "Invalid time format. Use '%Y-%m-%dT%H:%M:%S%z'.",
                        "error": "Invalid time format",
                    }
                })
        else:
            trigger_time_dt = client_current_time + timedelta(minutes=int(delay_in_minutes))

        # **Step 5: Calculate Delay Using User's Local Time**
        delay_seconds = (trigger_time_dt - client_current_time).total_seconds()

        if delay_seconds < 0:
            return json.dumps({
                "type": "reminder",
                "assistant": {
                    "status": "error",
                    "message": "Reminder time cannot be in the past.",
                    "error": "Invalid future time",
                }
            })

        # **Step 6: Generate Unique Reminder ID**
        reminder_uuid = str(uuid.uuid4())
        formatted_trigger_time = trigger_time_dt.strftime("%Y-%m-%dT%H:%M:%S%z")

        # **Step 7: Confirmation Response**
        confirmation_message = f"Reminder set for {formatted_trigger_time} to {task}."

        return json.dumps({
            "type": "reminder",
            "assistant": {
                "task": task,
                "trigger_time": formatted_trigger_time,
                "uuid": reminder_uuid,
                "message": confirmation_message,
                "reminder_message": f"It's time to {task}!",
                "delayedSeconds": int(delay_seconds),
                "status": "success"
            }
        })

    @function_tool(
        name="location_based_reminder",
        description=(
                "Sets a reminder to be triggered upon reaching a specified location. "
                "Speaks back in the same language as user."
        )
    )
    def location_based_reminder(
            self,
            task: Annotated[
                str,
                Field(
                    description="The task or reminder details provided by the user."
                ),
            ],
            place_name: Annotated[
                str,
                Field(
                    description="The name of the place for the reminder trigger."
                ),
            ],
            latitude: Annotated[
                float,
                Field(
                    description="The latitude coordinate of the location."
                ),
            ],
            longitude: Annotated[
                float,
                Field(
                    description="The longitude coordinate of the location."
                ),
            ],
    ) -> str:
        """
        Sets a location-based reminder.
        """
        # Generate a unique ID for the reminder
        reminder_uuid = str(uuid.uuid4())

        # Create confirmation and reminder messages
        confirmation_message = (
            f"Reminder set for task '{task}' at location '{place_name}' "
            f"with coordinates (Lat: {latitude}, Lon: {longitude})."
        )
        reminder_message = f"Reminder: {task} at {place_name}."

        # Prepare the response
        response = {
            "type": "location_based_reminder",
            "assistant": {
                "task": task,
                "trigger_location": {
                    "place_name": place_name,
                    "latitude": latitude,
                    "longitude": longitude,
                },
                "uuid": reminder_uuid,
                "message": confirmation_message,
                "reminder_message": reminder_message,
            },
        }

        print("location based reminder set for ", place_name)

        return json.dumps(response)

    @function_tool(
        name="cancel_reminder",
        description=(
                "Cancels a specific reminder by reviewing previous prompts to find matching reminders based on user request, "
                "trigger time, or location. Speaks back in the same language as user."
        )
    )
    def cancel_reminder(
            self,
            reminder_id: Annotated[
                str, Field(description="The unique identifier for the reminder to be canceled.")],
            task: Annotated[str, Field(description="The task associated with the reminder to be canceled.")],
            trigger_seconds: int = None,
            place_name: str = None,
    ) -> str:
        """
        Cancels a specific reminder.
        """
        if reminder_id:  # Simulate cancellation logic
            confirmation_message = f"Reminder '{task}' with ID {reminder_id} has been canceled."
            response = {
                "type": "cancel_reminder",
                "assistant": {
                    "reminder_id": reminder_id,
                    "task": task,
                    "message": confirmation_message,
                    "error": None,
                },
            }
        else:
            error_message = f"No matching reminder found for task '{task}'."
            response = {
                "type": "cancel_reminder",
                "assistant": {
                    "reminder_id": None,
                    "task": task,
                    "message": error_message,
                    "error": error_message,
                },
            }

        return json.dumps(response)

    @function_tool(
        name="list_reminders",
        description=(
                "Lists all active reminders set by the user. "
                "The first reminder to Buy groceries is an example and not to be sent or used in the response. "
                "Speaks back in the same language as user."
        )
    )
    def list_reminders(self) -> str:
        """
        Lists all active reminders.
        """
        # Simulate a list of reminders
        reminders = [
            {
                "task": "Buy groceries",
                "trigger_time": "2024-12-20T10:00:00",
                "uuid": str(uuid.uuid4()),
            },
        ]
        task_list = ", ".join([r["task"] for r in reminders])
        response = {
            "type": "list_reminders",
            "assistant": {
                "reminders": reminders,
                "message": f"Currently set reminders: {task_list}",
            },
        }
        return json.dumps(response)

    @function_tool(
        name="stop",
        description=(
                "Trigger this function only if the user explicitly asks to stop an activity, "
                "operation, or process within the vehicle system. Speaks back in the same language as user."
        )
    )
    def stop(
            self,
            activity: Annotated[str, Field(description="The activity or process to stop.")] = "current operation"
    ) -> str:
        """
        Stops an activity or process.
        """
        message = f"The current activity has been stopped as requested."
        response = {
            "type": "stop",
            "assistant": {
                "message": message,
            },
        }
        return json.dumps(response)

    @function_tool(
        name="call",
        description=(
                "Handle phone call requests by processing contact information and initiating calls. "
                "Speaks back in the same language as user."
                "This function will be invoked when user requests to make call to a contact or a number, for example: call mom or can you make a call to dad or call."
        )
    )
    async def call(
            self,
            contact_name: Annotated[
                str, Field(description="The name of the contact to call in english.")] = None,
            contact_number: Annotated[str, Field(description="The phone number to call.")] = None,
    ) -> str:
        """
        Handles phone call requests.
        """
        print("\n ====== CALL =======")
        if contact_name:
            message = f"Initiating a call to {contact_name}."
        elif contact_number:
            message = f"Calling the number {contact_number}."
        else:
            message = "No valid contact information provided. Unable to initiate a call."

        error = None if contact_name or contact_number else "No valid contact information found."

        response = {
            "type": "call",
            "assistant": {
                "message": message,
                "contact_name": contact_name or "",
                "contact_number": contact_number or "",
                "error": error,
            },
        }

        try:
            context = self.get_context()
            result = await context.room.local_participant.perform_rpc(
                destination_identity=get_participant().identity,
                method='rpc_by_client',
                payload=json.dumps(response)
            )
            print(f"Call result for call function : {result}")
            return result
        except Exception as e:
            print(f"RPC call failed: {e}")
            return "Failed to retrieve user location. " + str(e)

    @function_tool(
        name="sos",
        description=(
                "This function is triggered when the input indicates a potential emergency situation. "
                "It detects emergencies based on the user's input and provides a supportive "
                "response tailored to the context, offering assistance or guidance as needed."
                "the response should give a user friendly reassuring message and status of the emergency_assistance."
        )
    )
    def sos(self) -> str:
        """
        Detects emergency situations and provides a reassuring response.
        """
        print("\n ====== SOS =======")
        message = "We have detected an emergency. Please stay calm."
        emergency_assistance = "Immediate help is on the way."

        response = {
            "type": "sos",
            "assistant": {
                "message": message,
                "emergency_assistance": emergency_assistance,
            },
        }
        return json.dumps(response)

    GOOGLE_MAPS_API_KEY:Optional[str] = os.getenv("GOOGLE_MAP_KEY")
    SEARCH_RADIUS:Optional[int] = 1500  # Radius in meters for nearby searches

    def make_google_maps_request(self, url: str):
        """Helper function to fetch data from Google Maps API."""
        try:
            response = requests.get(url)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            return {"error": f"Failed to fetch data: {str(e)}"}

    @function_tool(
        name="find_nearest_location",
        description="Finds and returns the closest single location based on user input (e.g., 'Find the nearest gas station'). the input can be any type of place like gas station, atm, jeep service center, veg restaurant, non veg hotel, super market, hospital, nursery etc. be precise and short in your response and Make sure not to speak out any urls or links such as https://google.com.."
    )
    async def find_nearest_location(
            self,
            query: Annotated[str, Field(description="The type of place to find (e.g., 'gas station', 'ATM').")]
    ) -> str:
        """Finds the nearest location based on the user's GPS."""
        context = self.get_context()
        payload = json.dumps({
            "type": "location_request",
            "highAccuracy": True
        })
        response = await context.room.local_participant.perform_rpc(
            destination_identity=get_participant().identity,
            method='rpc_by_client',
            payload=payload
        )
        print(f"Call result for find nearest location: {response}")

        latitude, longitude = json.loads(response).get("latitude"), json.loads(response).get("longitude")

        if not latitude or not longitude:
            return json.dumps({"type": "error", "message": "User location not available."})

        url = (
            f"https://maps.googleapis.com/maps/api/place/nearbysearch/json?"
            f"location={latitude},{longitude}&radius={self.SEARCH_RADIUS}&keyword={query}&key={self.GOOGLE_MAPS_API_KEY}"
        )

        data = self.make_google_maps_request(url)
        places = data.get("results", [])

        if not places:
            return json.dumps({"type": "navigation", "assistant": {"message": f"No nearby {query} found."}})

        nearest_place = places[0]
        response = {
            "type": "navigation",
            "assistant": {
                "message": f"The nearest {query} is {nearest_place['name']} located at {nearest_place.get('vicinity', 'unknown location')}. Starting Navigation",
                "place_name": nearest_place["name"],
                "icon": nearest_place.get("icon", ""),
                "rating": nearest_place.get("rating", "N/A"),
                "category": nearest_place.get("types", []),
                "open_now": nearest_place.get("opening_hours", {}).get("open_now", "N/A"),
                "address": nearest_place.get("vicinity", "unknown location")
            }
        }
        return json.dumps(response)

    @function_tool(
        name="find_nearby_places",
        description="Lists multiple nearby locations within a specified radius (e.g., 'Show me restaurants nearby').the input can be any type of place like gas station, atm, jeep service center, veg restaurant, non veg hotel, super market, hospital, nursery etc. be precise and short in your response and Make sure not to speak out any urls or links such as https://google.com.."
    )
    async def find_nearby_places(
            self,
            query: Annotated[
                str, Field(description="The type of places to find (e.g., 'restaurants', 'coffee shops').")],
            count: Annotated[int, Field(description="Number of nearby places to list. Defaults to 4.")] = 4
    ) -> str:
        """Finds multiple nearby locations based on the user's GPS."""
        context = self.get_context()
        payload = json.dumps({
            "type": "location_request",
            "highAccuracy": True
        })
        response = await context.room.local_participant.perform_rpc(
            destination_identity=get_participant().identity,
            method='rpc_by_client',
            payload=payload
        )
        print(f"Call result for find nearby places: {response}")

        latitude, longitude = json.loads(response).get("latitude"), json.loads(response).get("longitude")
        if not latitude or not longitude:
            return json.dumps({"type": "error", "message": "User location not available."})

        url = (
            f"https://maps.googleapis.com/maps/api/place/nearbysearch/json?"
            f"location={latitude},{longitude}&radius={self.SEARCH_RADIUS}&keyword={query}&key={self.GOOGLE_MAPS_API_KEY}"
        )

        data = self.make_google_maps_request(url)
        places = data.get("results", [])[:count]

        if not places:
            return json.dumps({"type": "navigation", "assistant": {"message": f"No nearby {query} found."}})

        formatted_places = [{"name": p["name"],
                             "address": p.get("vicinity", "N/A"),
                             "icon": p.get("icon", ""),
                             "rating": p.get("rating", "N/A"),
                             "category": p.get("types", []),
                             "open_now": p.get("opening_hours", {}).get("open_now", "N/A"),} for p in places]

        response = {
            "type": "navigation",
            "assistant": {
                "message": f"Here are some nearby {query} locations:",
                "nearby_places": formatted_places
            }
        }
        return json.dumps(response)

    @function_tool(
        name="calculate_travel_time",
        description="Calculates estimated travel time and distance between two locations (e.g., 'How long to reach the airport?')."
    )
    async def calculate_travel_time(
            self,
            startLocation: Annotated[str, Field(description="The starting location.")],
            destination: Annotated[str, Field(description="The destination location.")]
    ) -> str:
        """Calculates travel time and distance from the user's location to the specified destination."""
        print("--- calculate_travel_time ---")
        if startLocation:
            latitude, longitude, city = getCoords(startLocation)
            print(f"start_latitude: {latitude}, start_longitude: {longitude}")
        else:
            context = self.get_context()
            payload = json.dumps({
                "type": "location_request",
                "highAccuracy": True
            })
            response = await context.room.local_participant.perform_rpc(
                destination_identity=get_participant().identity,
                method='rpc_by_client',
                payload=payload
            )
            print(f"Call result for calculate travel time : {response}")

            latitude, longitude = json.loads(response).get("latitude"), json.loads(response).get("longitude")
            if latitude and longitude is None:
                latitude, longitude = get_location()
            if not latitude or not longitude:
                return json.dumps({"type": "error", "message": "User location not available."})
        url = (
            f"https://maps.googleapis.com/maps/api/distancematrix/json?"
            f"origins={latitude},{longitude}&destinations={destination}&key={self.GOOGLE_MAPS_API_KEY}"
        )

        data = self.make_google_maps_request(url)
        print("data --> ", data)
        if data.get("status") != "OK":
            message = data.get("error_message", "Could not calculate travel time.")
            return json.dumps({"type": "navigation", "assistant": {"message": message}})
        rows = data.get("rows", [])

        if not rows or not rows[0].get("elements", []):
            return json.dumps({"type": "navigation", "assistant": {"message": "Could not calculate travel time."}})

        travel_info = rows[0]["elements"][0]
        distance = travel_info["distance"]["text"]
        duration = travel_info["duration"]["text"]

        response = {
            "type": "navigation",
            "assistant": {
                "message": f"The estimated travel time to {destination} is {duration}, covering a distance of {distance}.",
                "distance": distance,
                "duration": duration
            }
        }
        return json.dumps(response)

    @function_tool(
        name="navigate_to_destination",
        description="Provides step-by-step directions to a given destination. do not speak the url, just provide the url in the response. should be called everytime even if user asks multiple times."
    )
    async def navigate_to_destination(
            self,
            destination: Annotated[str, Field(description="The destination address or location.")]
    ) -> str:
        """Provides navigation directions to a specified destination."""

        # Get user location (latitude, longitude)
        print("destination: ", destination)
        context = self.get_context()
        payload = json.dumps({
            "type": "location_request",
            "highAccuracy": True
        })
        response = await context.room.local_participant.perform_rpc(
            destination_identity=get_participant().identity,
            method='rpc_by_client',
            payload=payload
        )
        print(f"Call result for navigate to destination: {response}")
        latitude, longitude = json.loads(response).get("latitude"), json.loads(response).get("longitude")
        if latitude and longitude is None:
            latitude, longitude = get_location()

        # If user location is unavailable
        if not latitude or not longitude:
            response = {
                "type": "navigation",
                "assistant": {
                    "message": f"Starting navigation to {destination}.",
                    "place_name": destination,
                    "query": "N/A",
                    "address": "N/A"
                }
            }

        # If user location is available
        else:
            navigation_url = f"https://www.google.com/maps/dir/?api=1&origin={latitude},{longitude}&destination={destination}"
            response = {
                "type": "navigation",
                "assistant": {
                    "message": f"Starting navigation to {destination}.",
                    "place_name": destination,
                    "query": "N/A",
                    "address": "N/A",
                    "navigation_url": navigation_url
                }
            }

        return json.dumps(response)

    @function_tool(
        name="live_data",
        description="Retrieves real-time data, including stocks, news, and other relevant information, using the Perplexity API."
                    "Ensures responses are accurate, concise, and up to date. Adapts the response language to match the user's input,"
                    "maintaining clarity and natural conversational flow."
    )
    async def live_data(
            self,
            request: Annotated[str, Field(description="The user's request for real-time information.")],
    ) -> str:

        try:
            # Prepare the API request payload
            url = "https://api.perplexity.ai/chat/completions"

            print("\n ===== Live Data Function ========")

            messages = [
                {
                    "role": "system",
                    "content": "You are a conversational AI designed to provide real-time information like stocks, news, and other updates."
                },
                {
                    "role": "user",
                    "content": request
                }
            ]

            payload = {
                "search_recency_filter": "month",
                "return_citations": True,
                "model": "llama-3.1-sonar-small-128k-online",
                "temperature": 0.2,
                "top_p": 0.9,
                "messages": messages,
                "search_domain_filter": ["perplexity.ai"],
                "return_images": False,
                "return_related_questions": False,
                "top_k": 0,
                "stream": False,
                "presence_penalty": 0,
                "frequency_penalty": 1,
            }

            headers = {
                "Authorization": "Bearer 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",
                "Content-Type": "application/json",
            }

            # Make the API request
            response = requests.post(url, json=payload, headers=headers)

            # Log and handle the response
            response.raise_for_status()  # Raise an error for HTTP issues
            response_data = response.json()

            # Extract and format the message content
            choices = response_data.get("choices", [])
            if choices:
                message = choices[0].get("message", {}).get("content", "No data found.")
                return json.dumps({
                    "type": "liveData",
                    "assistant": {
                        "message": message
                    }
                })

            # If no choices are available
            return json.dumps({
                "type": "liveData",
                "assistant": {
                    "message": "No results found for the query."
                }
            })

        except requests.exceptions.RequestException as e:
            logging.error(f"HTTP error while calling Perplexity API: {e}")
            return json.dumps({
                "type": "liveData",
                "assistant": {
                    "message": "Failed to connect to the server."
                }
            })

        except Exception as e:
            logging.error(f"Unexpected error in live_data: {e}")
            return json.dumps({
                "type": "liveData",
                "assistant": {
                    "message": "An unexpected error occurred."
                }
            })

    @function_tool(
        name="vehicle_expert",
        description="Answers vehicle-related questions such as troubleshooting, maintenance, and specifications. Speaks back in the same language as user."
    )
    async def vehicle_expert(
            self,
            transcription: Annotated[
                str, Field(description="The user's question about vehicles.")
            ]
    ) -> str:

        url = os.getenv("VEHICLE_EXPERT_URL")
        data = {"question": transcription}

        try:
            # Call the Vehicle Expert API
            logging.info(f"Sending request to Vehicle Expert API: {url}")
            response = requests.post(url, json=data)

            if response.status_code == 200:
                api_response = response.json()
                logging.info(f"Vehicle Expert API response: {api_response}")
                return json.dumps({
                    "type": "vehicleExpert",
                    "assistant": api_response
                })
            else:
                logging.error(f"Vehicle Expert API returned status code {response.status_code}")
                return json.dumps({
                    "type": "error",
                    "message": "The server is not reachable"
                })

        except requests.exceptions.RequestException as e:
            logging.error(f"Error while calling Vehicle Expert API: {e}")
            return json.dumps({
                "type": "error",
                "message": "The server is not reachable"
            })

    @function_tool(
        name="smart_car",
        description="Provides information about the user's vehicle, such as speeed, location of vehicle, fuel level, battery status, and maintenance alerts."
    )
    async def smart_car(self) -> str:
        """
        Fetches real-time data about the user's vehicle.
        """
        try:
            context = self.get_context()
            payload = json.dumps({
                "type": "smart_car_request"
            })
            response = await context.room.local_participant.perform_rpc(
                destination_identity=get_participant().identity,
                method='rpc_by_client',
                payload=payload
            )
            print(f"Call result for smart car: {response}")
            return response
        except Exception as e:
            print(f"RPC call failed: {e}")
            return "Failed to retrieve vehicle data. " + str(e)

    @function_tool(
        name="get_user_location",
        description="gets user location only when user asks get my location or what is my location or where am i and so on regarding his current location."
    )
    async def get_user_location(self, high_accuracy: Annotated[bool, Field(description="Whether to use high accuracy mode, which is slower")] = False)-> str:
        """Retrieve the user's current geolocation as lat/lng."""
        try:
            context = self.get_context()
            payload = json.dumps({
                "type": "location_request",
                "highAccuracy": high_accuracy
            })
            response = await context.room.local_participant.perform_rpc(
                destination_identity=get_participant().identity,
                method='rpc_by_client',
                payload=payload
            )
            print(f"Call result for get user location: {response}")
            latitude, longitude = json.loads(response).get("latitude"), json.loads(response).get("longitude")

            print(f"User location: {latitude}, {longitude}")
            address = getAddress(latitude, longitude)
            if address:
                print(f"User address: {address}")
                response = {
                    "type": "currentLocation",
                    "latitude": latitude,
                    "longitude": longitude,
                    "address": address
                }
            else:
                response = {
                    "type": "currentLocation",
                    "message": "Failed to fetch address for the location."
                }
            return json.dumps(response)
        except Exception as e:
            print(f"RPC call failed: {e}")
            return "Failed to retrieve user location. " + str(e)

    # @function_tool(
    #     name="check_account_balance",
    #     description="Fetches the account balance for a given account number. Returns the balance in the user's preferred currency."
    # )
    # async def check_account_balance(
    #         self,
    #         account_number: Annotated[str, Field(description="The user's account number.")],
    # ) -> str:
    #     """
    #     Fetches the account balance for the given account number.
    #     """
    #     balance = 10000.00
    #     currency = "INR"
    #     return json.dumps({
    #         "type": "banking",
    #         "assistant": {
    #             "message": f"Your account balance is {balance} {currency}.",
    #             "balance": balance,
    #             "currency": currency
    #         }
    #     })
    #
    # @function_tool(
    #     name="fetch_recent_transactions",
    #     description="Retrieves recent transactions of user. Returns a example list of the last 5 transactions. reads the statement in a user friendly way."
    # )
    # async def fetch_recent_transactions(
    #         self,
    #         transactions: Annotated[str, Field(description="The account number to fetch transactions for.")],
    # ) -> str:
    #     """
    #     Fetches recent transactions for the provided account number.
    #     """
    #     try:
    #         # Simulate fetching recent transactions
    #         transactions = [
    #             {"date": "2022-01-01", "description": "Payment", "amount": 100.00, "currency": "INR"},
    #             {"date": "2022-01-02", "description": "Withdrawal", "amount": 500.00, "currency": "INR"},
    #             {"date": "2022-01-03", "description": "Deposit", "amount": 1000.00, "currency": "INR"},
    #             {"date": "2022-01-04", "description": "Payment", "amount": 200.00, "currency": "INR"},
    #             {"date": "2022-01-05", "description": "Withdrawal", "amount": 300.00, "currency": "INR"},
    #         ]
    #         if not transactions:
    #             return json.dumps({
    #                 "type": "banking",
    #                 "assistant": {
    #                     "message": "No recent transactions found.",
    #                     "transactions": []
    #                 }
    #             })
    #
    #         formatted_transactions = [
    #             {
    #                 "date": t["date"],
    #                 "description": t["description"],
    #                 "amount": t["amount"],
    #                 "currency": t["currency"]
    #             } for t in transactions[:5]
    #         ]
    #
    #         return json.dumps({
    #             "type": "banking",
    #             "assistant": {
    #                 "message": "Here are your last 5 transactions:",
    #                 "transactions": formatted_transactions
    #             }
    #         })
    #     except Exception as e:
    #         return json.dumps({
    #             "type": "error",
    #             "message": f"Failed to retrieve transactions. Error: {str(e)}"
    #         })
    #
    # @function_tool(
    #     name="pay_bill",
    #     description="Initiates a bill payment process for the user. Returns a confirmation message."
    # )
    # async def pay_bill(
    #         self,
    #         bill_amount: Annotated[float, Field(description="The amount to pay.")],
    #         bill_type: Annotated[str, Field(description="The type of bill to pay.")],
    # ) -> str:
    #     """
    #     Initiates a bill payment process for the user.
    #     """
    #     try:
    #         # Simulate a bill payment process
    #         confirmation_message = f"Successfully paid {bill_amount} for {bill_type}."
    #         return json.dumps({
    #             "type": "banking",
    #             "assistant": {
    #                 "message": confirmation_message
    #             }
    #         })
    #     except Exception as e:
    #         return json.dumps({
    #             "type": "error",
    #             "message": f"Failed to process payment. Error: {str(e)}"
    #         })
    #
    # @function_tool(
    #     name="transfer_funds",
    #     description="Initiates a fund transfer process between accounts. Returns a confirmation message."
    # )
    # async def transfer_funds(
    #         self,
    #         amount: Annotated[float, Field(description="The amount to transfer.")],
    #         recipient: Annotated[str, Field(description="The recipient's account number.")],
    # ) -> str:
    #     """
    #     Initiates a fund transfer process between accounts.
    #     """
    #     try:
    #         # Simulate a fund transfer process
    #         confirmation_message = f"Successfully transferred {amount} to {recipient}."
    #         return json.dumps({
    #             "type": "banking",
    #             "assistant": {
    #                 "message": confirmation_message
    #             }
    #         })
    #     except Exception as e:
    #         return json.dumps({
    #             "type": "error",
    #             "message": f"Failed to transfer funds. Error: {str(e)}"
    #         })
    #
    # @function_tool(
    #     name="check_loan_eligibility",
    #     description="Checks the user's eligibility for a loan based on the provided details. Returns the eligibility status."
    # )
    # async def check_loan_eligibility(
    #         self,
    #         monthly_income: Annotated[float, Field(description="The user's monthly income.")],
    #         loan_amount: Annotated[float, Field(description="The loan amount requested.")],
    #         credit_score: Annotated[int, Field(description="The user's credit score.")],
    # ) -> str:
    #     """
    #     Checks the user's eligibility for a loan based on the provided details.
    #     """
    #     try:
    #         # Simulate a loan eligibility check
    #         eligibility_status = "Eligible" if credit_score >= 700 else "Not Eligible"
    #         return json.dumps({
    #             "type": "banking",
    #             "assistant": {
    #                 "message": f"Loan eligibility status: {eligibility_status}"
    #             }
    #         })
    #     except Exception as e:
    #         return json.dumps({
    #             "type": "error",
    #             "message": f"Failed to check loan eligibility. Error: {str(e)}"
    #         })

    # @function_tool(
    #     name="save_user_context",
    #     description=(
    #         "Captures each and every conversation between user and AI assistant to remember the context of the user. "
    #         "this function will be triggered everytime user input is received and the AI response is sent. every time an AI responds this function will be triggered"
    #         "make sure to save the conversation in the memory db everytime user input is received and the AI response is sent."
    #         "Do not mention the user about saving the data which may annoy the user. "
    #     )
    # )
    # def save_user_context(
    #     self,
    #     data_type: Annotated[str, Field(description=(
    #         "The category of data being saved (e.g., 'name', 'relationship', 'location', 'preference', 'opinion')."
    #     ))],
    #     user_input: Annotated[str, Field(description=(
    #         "The user's input for every conversation (e.g., 'My name is Arjun', 'I dislike noisy places', 'My home is in Paris', "
    #         "'John is my friend', 'The coffee shop near my office sells great cappuccinos', 'what is the weather today', what is the stock price of stellantis)."
    #     ))] = None,
    #     ai_response: Annotated[str, Field(description=(
    #         "The AI's response to the user's input (e.g., 'Nice to meet you, Arjun!', "
    #         "'I’ll remember your dislike for noisy places.', 'Paris is a great place, i would love to visit paris one day if i could and i will remember your home is at paris.', 'Sounds good, I hope you will introduce your friend john to me one day.', "
    #         "'I’ve noted that the coffee shop near your office has great cappuccinos.', 'weather in banglore is hazy, temperature, wind, humidity', 'stock price of stellantis today is')."
    #     ))] = None,
    # ) -> str:
    #
    #     """
    #     Saves data as requested by the user.
    #     """
    #     print("\n ====== Save User Context =======")
    #     participant_userId = get_participant().identity
    #     success_message = f"Saved {user_input} for user {participant_userId}."
    #     # save_in_memory_db(user_input, ai_response, participant_userId)
    #
    #     print(f"data saved in DB Successfully")
    #     return json.dumps({
    #         "type": "save_data",
    #         "assistant": {
    #             "status": "success",
    #             "message": success_message,
    #             "error": None,
    #         },
    #     })

    @function_tool(
        name="save_user_preferences",
        description="Stores user preferences such as favorite food, music, activities, or dislikes."
    )
    def save_user_preferences(
            self,
            preference: Annotated[str, Field(description="The preference or dislike expressed by the user.")],
            context: Annotated[
                str, Field(description="Additional context (e.g., 'music', 'food', 'weather').")] = None
    ) -> str:
        """
        Saves user preferences in the memory database.
        """
        participant_id = self.get_context().room.local_participant.identity
        ai_response = f"I'll remember that you {preference.lower()}."

        print(f"🔹 Saving Preference: {preference} (Context: {context}) for {participant_id}")
        save_in_memory_db(preference, ai_response, participant_id)

        return json.dumps({
            "type": "save_preferences",
            "assistant": {
                "status": "success",
                "message": ai_response ,
                "error": None
            }
        })

    @function_tool(
        name="save_user_relationships",
        description="Stores information about people related to the user (e.g., friends, family, colleagues)."
    )
    def save_user_relationships(
            self,
            person_name: Annotated[str, Field(description="The name of the person (e.g., 'John', 'Mom').")],
            relationship: Annotated[
                str, Field(description="The relationship to the user (e.g., 'friend', 'brother').")]
    ) -> str:
        """
        Saves relationships in the memory database.
        """

        participant_id = self.get_context().room.local_participant.identity
        ai_response = f"I'll remember that {person_name} is your {relationship}."

        print(f"🔹 Saving Relationship: {person_name} ({relationship}) for {participant_id}")
        save_in_memory_db(person_name, ai_response, participant_id)

        return json.dumps({
            "type": "save_relationships",
            "assistant": {
                "status": "success",
                "message": ai_response ,
                "error": None
            }
        })

    @function_tool(
        name="save_user_location",
        description="Stores user location details such as home, work, or frequently visited places."
    )
    def save_user_location(
            self,
            place_name: Annotated[str, Field(description="The name of the location (e.g., 'Home', 'Office').")],
            address: Annotated[str, Field(description="The full address or city name.")] = None
    ) -> str:
        """
        Saves user location data.
        """

        participant_id = self.get_context().room.local_participant.identity
        ai_response = f"I've saved '{place_name}' as {address or 'your location'}."

        print(f"🔹 Saving Location: {place_name} ({address}) for {participant_id}")
        save_in_memory_db(place_name, ai_response, participant_id)

        return json.dumps({
            "type": "save_location",
            "assistant": {
                "status": "success",
                "message": ai_response,
                "error": None
            }
        })

    @function_tool(
        name="save_user_travel_info",
        description="Whenever user gives any informations regrading his travel plans, Stores user travel details, including destination, date, and purpose."
    )
    def save_user_travel_info(
            self,
            destination: Annotated[str, Field(description="The travel destination (e.g., 'Paris', 'New York').")],
            travel_date: Annotated[str, Field(description="The date of travel in YYYY-MM-DD format.")],
            purpose: Annotated[str, Field(description="The purpose of travel (e.g., 'business', 'vacation').")] = None
    ) -> str:
        """
        Saves user travel plans in the memory database.
        """

        participant_id = participant_userId = get_participant().identity

        # Normalize date format (remove spaces and check validity)
        travel_date = travel_date.strip()

        try:
            parsed_date = datetime.strptime(travel_date, "%Y-%m-%d")  # Validate format
        except ValueError:
            return json.dumps({
                "type": "save_travel",
                "assistant": {
                    "status": "error",
                    "message": f"Invalid date format: '{travel_date}'. Please use YYYY-MM-DD format (e.g., 2025-05-10).",
                    "error": "Invalid date"
                }
            })

        # Convert to a user-friendly format (e.g., "May 10, 2025")
        formatted_date = parsed_date.strftime("%B %d, %Y")

        ai_response = f"I've saved your travel plan to {destination} on {formatted_date}."

        print(f"🔹 Saving Travel Info: {destination} on {formatted_date} for {purpose} (User: {participant_id})")
        save_in_memory_db(f"Travel to {destination} on {formatted_date}", ai_response, participant_id)

        return json.dumps({
            "type": "save_travel",
            "assistant": {
                "status": "success",
                "message": ai_response,
                "error": None
            }
        })

    @function_tool(
        name="get_user_data",
        description="Fetches users data such as name, home, office, or other saved informations about the user. for example datas like users name, home, office, likes, dislikes, family members name, firends names and thier addresses, dreams, ambitions, and much more data about the user. if you dont know any information about user search here. Returns a message if no data is available. this function should not be used when user asks to call or message a contact."
    )
    def get_user_data(
        self,
        data_type: Annotated[str, Field(description="The type of data being fetched (e.g., 'home', 'office').")],
    ) -> str:
        """
        Retrieves user data if available. Returns a message if no data is stored for the requested type.
        """
        participant_userId = get_participant().identity
        participant_userId = participant_userId.decode('utf-8') if isinstance(participant_userId, bytes) else participant_userId
        print(f"Fetching '{data_type}' for participant {participant_userId}.")

        try:
            payload = {
                "user_device_id": participant_userId,
                "user_query": data_type,
            }

            memorydb_url = os.getenv("MEMORYDB_URL")
            if not memorydb_url:
                raise ValueError("MEMORYDB_URL environment variable is not set.")

            response = requests.post(f"{memorydb_url}/api/v1/get_user_data", json=payload)
            print(f"MemoryDB response: {response.status_code}, {response.text}")
            if response.status_code == 200:
                response_data = response.json()

                if response_data.get("status") == "success" and "data" in response_data:
                    # Remove unwanted backslashes from the content
                    cleaned_data = [
                        {
                            "role": item["role"],
                            "content": item["content"].replace("\\", "")
                        }
                        for item in response_data["data"]
                    ]
                    return json.dumps({
                        "type": "get_user_data",
                        "assistant": {
                            "status": "Success",
                            "message": f"These are previous chat related to the user query:",
                            "data": cleaned_data
                        }
                    })
                else:
                    return json.dumps({
                        "type": "get_user_data",
                        "assistant": {
                            "status": "failure",
                            "message": "No data found or unexpected response structure."
                        }
                    })

            else:
                return json.dumps({
                    "type": "get_user_data",
                    "assistant": {
                        "status": "failure",
                        "message": f"MemoryDB returned an error: {response.status_code}, {response.text}"
                    }
                })

        except requests.exceptions.RequestException as e:
            return json.dumps({
                "type": "get_user_data",
                "assistant": {
                    "status": "failure",
                    "message": f"Error connecting to MemoryDB service: {str(e)}"
                }
            })
        except ValueError as ve:
            return json.dumps({
                "type": "get_user_data",
                "assistant": {
                    "status": "failure",
                    "message": str(ve)
                }
            })
