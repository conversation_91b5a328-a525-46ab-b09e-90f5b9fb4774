# weather_function.py

import os
import aiohttp
import json
import logging
from livekit.agents.llm import ai_callable, FunctionContext
from livekit.agents import llm
from typing import Annotated

class WeatherFunction(FunctionContext):

    @ai_callable(
        name="get_weather_by_location",
        description=(
            "Fetches the weather data for the specified location. "
            "Returns the complete JSON response but speaks a short and clear message to the user. "
            "Should speak back in the same language as the user."
        )
    )
    async def get_weather_by_location(
            self,
            location: Annotated[
                str, llm.TypeInfo(
                    description="The location user mentioned to get weather information for."
                )
            ],
            timestamp: Annotated[
                str, llm.TypeInfo(
                    description="The timestamp of the user's request in Unix time, UTC time zone."
                )
            ] = None,
    ):
        api_key = os.environ.get("OPENWEATHER_API_KEY")
        url = f"https://api.openweathermap.org/data/2.5/weather?q={location}&dt={timestamp}&appid={api_key}&units=metric"
        return await self._fetch_weather_data(url)

    @ai_callable(
        name="get_weather_by_coords",
        description=(
            "Fetches the weather data for the current location based on latitude and longitude. "
            "Returns the complete JSON response but speaks a short and clear message to the user. "
            "Should speak back in the same language as the user."
        )
    )
    async def get_weather_by_coords(
            self,
            latitude: Annotated[
                float, llm.TypeInfo(
                    description="The latitude coordinate of the location."
                )
            ],
            longitude: Annotated[
                float, llm.TypeInfo(
                    description="The longitude coordinate of the location."
                )
            ],
            timestamp: Annotated[
                str, llm.TypeInfo(
                    description="The timestamp of the user's request in Unix time, UTC time zone."
                )
            ] = None,
    ):
        api_key = os.environ.get("OPENWEATHER_API_KEY")
        url = f"https://api.openweathermap.org/data/2.5/weather?lat={latitude}&lon={longitude}&dt={timestamp}&appid={api_key}&units=metric"
        return await self._fetch_weather_data(url)

    async def _fetch_weather_data(self, url: str) -> str:
        async with aiohttp.ClientSession() as session:
            async with session.get(url) as response:
                if response.status == 200:
                    weather_data = await response.json()
                    temperature = weather_data.get("main", {}).get("temp")
                    feels_like = weather_data.get("main", {}).get("feels_like")
                    description = weather_data.get("weather", [{}])[0].get("description")
                    wind_speed = weather_data.get("wind", {}).get("speed")
                    wind_direction = weather_data.get("wind", {}).get("deg")
                    humidity = weather_data.get("main", {}).get("humidity")

                    result = {
                        "type": "weather",
                        "assistant": {
                            "message": (
                                f"The current weather is {description}. "
                                f"The temperature is {temperature}°C, feels like {feels_like}°C. "
                                f"Wind speed is {wind_speed} m/s from {wind_direction}°. Humidity is {humidity}%."
                            ),
                            "temperature": f"{temperature}",
                            "feels_like": f"{feels_like}",
                            "description": f"{description}",
                            "wind_speed": f"{wind_speed}",
                            "wind_direction": f"{wind_direction}",
                            "humidity": f"{humidity}",
                        }
                    }
                    return json.dumps(result)
                else:
                    return json.dumps({
                        "type": "weather",
                        "assistant": {
                            "message": f"Failed to get weather data, status code: {response.status}.",
                        }
                    })