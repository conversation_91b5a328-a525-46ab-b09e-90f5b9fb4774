import json
import logging
import os
import requests
from vo.user_location import get_location


def save_in_memory_db(
        request: str,
        response: str,
        participant_id: str,
) -> str:
    """
    Calls the save method for each user request and response.

    Args:
        request
        response
    Returns:
        str: A JSON string containing the vehicle expert's response or an error message.
    """
    try:
        latitude, longitude = get_location()
        if latitude is None or longitude is None:
            logging.error("Failed to get location. Using default location.")
            latitude, longitude = 12.878787, 77.590694

        # participant_userId = participant_userId.decode('utf-8')

        try:
            payload = {
                "user_query": request,
                "bot_response": response,
                "user_device_id": participant_id,
                "latitude": latitude,  # Add actual latitude if available
                "longitude": longitude,  # Add actual longitude if available
            }
            logging.info(f"Sending paylod to DB {payload}")
            memory_db_url = os.getenv("MEMORYDB_URL")
            response = requests.post(f"{memory_db_url}/api/v1/save_data", json=payload)
            logging.info(f"Successfully stored data")
            return response.json()
        except requests.exceptions.RequestException as e:
            return {"status": "failure", "message": f"Error connecting to MemoryDB service: {str(e)}"}

    except requests.exceptions.RequestException as e:
        logging.error(f"Error while saving the request and response: {e}")
        return json.dumps({
            "type": "error",
            "message": "The server has failed to save the input and response."
        })


def get_in_memory_db(participant_userid: str, querry: str) -> str:
    """
    Calls the get method for each user request and response.

    Args:
        participant_id
    Returns:
        str: A JSON string containing the vehicle expert's response or an error message.
    """
    try:
        logging.info(f"Sending request to get data from memory db: {participant_userid}")
        payload = {
            "user_device_id": participant_userid,
            "user_query": querry,
        }

        memorydb_url = os.getenv("MEMORYDB_URL")
        if not memorydb_url:
            raise ValueError("MEMORYDB_URL environment variable is not set.")
        response = requests.post(f"{memorydb_url}/api/v1/get_user_data", json=payload)
        return response.json()

    except requests.exceptions.RequestException as e:
        logging.error(f"Error while fetching the request and response: {e}")
        return json.dumps({
            "type": "error",
            "message": "The server has failed to fetch the input and response."
        })