import json
import aiohttp
from datetime import datetime, timedelta, timezone


async def fetch_weather_data(url: str):
    """Fetch weather data from OpenWeather API."""
    async with aiohttp.ClientSession() as session:
        async with session.get(url) as response:
            print(f"Fetching weather data from {url}...")
            if response.status == 200:
                return await response.json()
            return {"error": f"Failed to fetch weather data, status code: {response.status}"}


def convert_unix_to_time(unix_timestamp, timezone_offset):
    """Convert Unix timestamp to readable local time."""
    # Convert Unix timestamp to UTC time
    utc_time = datetime.utcfromtimestamp(unix_timestamp).replace(tzinfo=timezone.utc)

    # Adjust for the timezone offset
    local_time = utc_time + timedelta(seconds=timezone_offset)
    print(f"Converted Unix timestamp {unix_timestamp} to local time {local_time}")

    # Format the time in HH:MM AM/PM
    return local_time.strftime("%I:%M %p")  # Example: "06:45 AM"


def format_weather_response(weather_data: dict, city: str, is_future: bool = False):
    """Formats weather data into structured JSON responses for current, past, and future weather."""
    if "error" in weather_data:
        return json.dumps({"type": "weather", "assistant": {"message": weather_data["error"]}})

    # 🟢 **Current Weather Response**
    if "current" in weather_data:
        temperature = weather_data["current"].get("temp")
        feels_like = weather_data["current"].get("feels_like")
        description = weather_data["current"]["weather"][0]["description"]
        wind_speed = weather_data["current"].get("wind_speed")
        wind_direction = weather_data["current"].get("wind_deg")
        humidity = weather_data["current"].get("humidity")
        time_zone = weather_data.get("timezone")
        timezone_offset = weather_data.get("timezone_offset", 0)  # Default to 0 if not present

        sunrise_unix = weather_data["current"].get("sunrise")
        sunset_unix = weather_data["current"].get("sunset")

        # Convert sunrise and sunset times
        sunrise = convert_unix_to_time(sunrise_unix, timezone_offset)
        sunset = convert_unix_to_time(sunset_unix, timezone_offset)

        return json.dumps({
            "type": "weather",
            "assistant": {
                "message": (
                    f"The current weather is {description}. "
                    f"The temperature is {temperature}°C, feels like {feels_like}°C. "
                    f"Wind speed is {wind_speed} m/s from {wind_direction}°. Humidity is {humidity}%."
                ),
                "temperature": f"{temperature}",
                "feels_like": f"{feels_like}",
                "description": f"{description}",
                "wind_speed": f"{wind_speed}",
                "wind_direction": f"{wind_direction}",
                "humidity": f"{humidity}",
                "sunrise": sunrise,
                "sunset": sunset,
                "city": f"{city}",
            },
            "openweather_response": "NA"
        })

    # 🔵 **Past Weather Response**
    if "data" in weather_data and weather_data["data"]:
        past_data = weather_data["data"][0]
        temperature = past_data.get("temp")
        feels_like = past_data.get("feels_like")
        description = past_data["weather"][0]["description"]
        wind_speed = past_data.get("wind_speed")
        wind_direction = past_data.get("wind_deg")
        humidity = past_data.get("humidity")

        return json.dumps({
            "type": "weather",
            "assistant": {
                "message": (
                    f"The weather was {description}. "
                    f"The temperature was {temperature}°C, felt like {feels_like}°C. "
                    f"Wind speed was {wind_speed} m/s from {wind_direction}°. Humidity was {humidity}%."
                ),
                "temperature": f"{temperature}",
                "feels_like": f"{feels_like}",
                "description": f"{description}",
                "wind_speed": f"{wind_speed}",
                "wind_direction": f"{wind_direction}",
                "humidity": f"{humidity}",
                "city": f"{city}",
            },
            "openweather_response": weather_data
        })

    # 🔴 **Future Weather Response (Updated Format)**
    if is_future and "daily" in weather_data and weather_data["daily"]:
        forecast_list = [
            {
                "date": datetime.utcfromtimestamp(day["dt"]).strftime("%Y-%m-%d"),
                "temperature": f"{day['temp']['day']}",
                "feels_like": f"{day['feels_like']['day']}",
                "description": day["weather"][0]["description"],
                "wind_speed": f"{day.get('wind_speed', 'N/A')}",
                "wind_direction": f"{day.get('wind_deg', 'N/A')}",
                "humidity": f"{day.get('humidity', 'N/A')}",
            }
            for day in weather_data["daily"]
        ]

        message = f"Here's the weather forecast for the next {len(forecast_list)} days!"

        return json.dumps({
            "type": "weather_forecast",
            "assistant": {
                "message": message,
                "city": f"{city}",
                "daywise_weather": forecast_list
            },
            "openweather_response": weather_data
        })

    # ❌ **Invalid Data Format Fallback**
    return json.dumps({"type": "weather", "assistant": {"message": "Invalid weather data format received."}})