import base64
import requests
import os
import json
from typing import List

# Store your credentials in environment variables
SPOTIFY_CLIENT_ID = os.getenv("SPOTIFY_CLIENT_ID")
SPOTIFY_CLIENT_SECRET = os.getenv("SPOTIFY_CLIENT_SECRET")


def get_spotify_token():
    """
    Requests an access token from Spotify API using Client Credentials flow.
    """
    if not SPOTIFY_CLIENT_ID or not SPOTIFY_CLIENT_SECRET:
        print("Error: SPOTIFY_CLIENT_ID or SPOTIFY_CLIENT_SECRET is missing!")
        return None

    url = "https://accounts.spotify.com/api/token"

    # Encode Client ID and Secret to Base64
    auth_header = base64.b64encode(f"{SPOTIFY_CLIENT_ID}:{SPOTIFY_CLIENT_SECRET}".encode()).decode()

    headers = {
        "Authorization": f"Basic {auth_header}",
        "Content-Type": "application/x-www-form-urlencoded",
    }
    data = {"grant_type": "client_credentials"}

    response = requests.post(url, headers=headers, data=data)

    if response.status_code == 200:
        token_info = response.json()
        print("✅ Spotify Token Retrieved Successfully!")
        return token_info["access_token"]
    else:
        print(f"❌ Failed to get Spotify token: {response.json()}")
        return None


def fetch_songs_by_artist(artist_name: str) -> List[dict]:
    """
    Fetches top tracks for a given artist using the Spotify API.
    """
    token = get_spotify_token()
    if not token:
        return []

    search_url = f"https://api.spotify.com/v1/search?q={artist_name}&type=artist&limit=1"
    headers = {"Authorization": f"Bearer {token}"}

    search_response = requests.get(search_url, headers=headers)
    if search_response.status_code != 200:
        print("❌ Error fetching artist ID:", search_response.text)
        return []

    search_data = search_response.json()
    artists = search_data.get("artists", {}).get("items", [])
    if not artists:
        return []

    artist_id = artists[0]["id"]  # Get the first matched artist ID

    # Fetch the artist's top tracks
    top_tracks_url = f"https://api.spotify.com/v1/artists/{artist_id}/top-tracks?market=US"
    tracks_response = requests.get(top_tracks_url, headers=headers)

    if tracks_response.status_code != 200:
        print("❌ Error fetching tracks:", tracks_response.text)
        return []

    track_data = tracks_response.json().get("tracks", [])
    print("✅ Fetched top tracks for artist:", artist_name)

    return [
        {
            "song_name": track["name"],
            "artists": ", ".join([artist["name"] for artist in track["artists"]]),
            # Multiple artists separated by comma
            "spotify_url": track["external_urls"]["spotify"],
            "preview_url": track.get("preview_url", "No preview available"),
            "is_playable": track.get("is_playable", False),
            "popularity": track.get("popularity", 0),  # Popularity Score (0-100)
            "duration_ms": track["duration_ms"],  # Song length in milliseconds
            "cover_image": track["album"]["images"][0]["url"] if track["album"]["images"] else None,
            "album_name": track["album"]["name"],
            "release_date": track["album"]["release_date"],
            "album_type": track["album"]["album_type"],
            "total_tracks": track["album"].get("total_tracks", "N/A"),
        }
        for track in track_data[:5]  # Limit to top 5 tracks
    ]

def search_song_on_spotify(song: str, artist: str = None):
    """
    Searches for a specific song on Spotify and returns its details.
    """
    token = get_spotify_token()
    if not token:
        return None

    url = f"https://api.spotify.com/v1/search?q={song}&type=track&limit=1"
    headers = {"Authorization": f"Bearer {token}"}

    response = requests.get(url, headers=headers)

    if response.status_code == 200:
        print("✅ Fetched song details from Spotify!", response.json())
        tracks = response.json().get("tracks", {}).get("items", [])
        if tracks:
            track = tracks[0]  # Get the first matched track
            return {
                "song_name": track["name"],
                "artists": ", ".join([a["name"] for a in track["artists"]]),
                "spotify_url": track["external_urls"]["spotify"],
                "preview_url": track.get("preview_url", "No preview available"),
                "is_playable": track.get("is_playable", False),
                "duration_ms": track["duration_ms"],
                "album_name": track["album"]["name"],
                "release_date": track["album"]["release_date"],
                "cover_image": track["album"]["images"][0]["url"] if track["album"]["images"] else None,
            }
    return None

def search_tracks_on_spotify(query: str, limit: int = 10) -> list:
    token = get_spotify_token()
    if not token:
        return []

    url = f"https://api.spotify.com/v1/search?q={query}&type=track&limit={limit}"
    headers = {"Authorization": f"Bearer {token}"}

    response = requests.get(url, headers=headers)

    if response.status_code != 200:
        print(f"❌ Failed to fetch tracks: {response.text}")
        return []

    tracks_data = response.json().get("tracks", {}).get("items", [])

    return [
        {
            "song_name": track["name"],
            "artists": ", ".join([artist["name"] for artist in track["artists"]]),
            "spotify_url": track["external_urls"]["spotify"],
            "preview_url": track.get("preview_url", "No preview available"),
            "is_playable": track.get("is_playable", False),
            "duration_ms": track["duration_ms"],
            "album_name": track["album"]["name"],
            "release_date": track["album"]["release_date"],
            "cover_image": track["album"]["images"][0]["url"] if track["album"]["images"] else None,
        }
        for track in tracks_data
    ]
