import os
import requests

def getAddress(latitude, longitude):
    google_map_key = os.getenv("GOOGLE_MAP_KEY")
    google_geocode_url = os.getenv("GOOGLE_GEOCODE_URL")
    url = f"{google_geocode_url}json?latlng={latitude},{longitude}&key={google_map_key}"
    # Send GET request to the API
    response = requests.get(url)

    if response.status_code == 200:
        data = response.json()
        if data["status"] == "OK":
            results = data.get("results")
            if len(results) > 0:
                address = results[0].get("formatted_address", "No address found")
                return address
            else:
                return "No address found"
        else:
            return "No address found"
    else:
        return "No address found"

def getCoords(address):
    print(f"getting coords for address : {address}")
    google_map_key = os.getenv("GOOGLE_MAP_KEY")
    google_geocode_url = os.getenv("GOOGLE_GEOCODE_URL")
    address = address.replace(" ", "+")
    url = f"{google_geocode_url}json?address={address}&key={google_map_key}"
    # Send GET request to the API
    response = requests.get(url)

    if response.status_code == 200:
        data = response.json()
        if data["status"] == "OK":
            results = data.get("results")
            print(f"results from geocode : {results}")
            if results:
                location = results[0]["geometry"]["location"]
                long_name = results[0]["address_components"][0]["long_name"]
                return location["lat"], location["lng"], long_name
        else:
            return "No address found"
    else:
        return "No address found"