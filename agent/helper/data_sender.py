from livekit import rtc
from livekit.agents import JobContext
import json

async def send_transcription(ctx: JobContext, participant: rtc.Participant, track_sid: str, segment_id: str, text: str, is_final: bool = True):
    transcription = rtc.Transcription(
        participant_identity=participant.identity,
        track_sid=track_sid,
        segments=[
            rtc.TranscriptionSegment(
                id=segment_id,
                text=text,
                start_time=0,
                end_time=0,
                language="en",
                final=is_final,
            )
        ],
    )
    await ctx.room.local_participant.publish_transcription(transcription)

async def send_json_to_client(ctx: JobContext, participant: rtc.Participant, data: dict):
    """Sends JSON data to the client."""
    try:
        json_data = json.dumps(data)
        await ctx.room.local_participant.publish_data(
            json_data,
            reliable=True,
            destination_identities=[participant.identity],
            topic="function_call_result"
        )
        print(f"JSON sent to client: {json_data}")
    except Exception as e:
        print(f"Failed to send JSON to client: {e}")