from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware # type: ignore
import logging
import logging.config

from fastapi import APIRouter # type: ignore
from endpoints import conv
import os

log = logging.getLogger(__name__)

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S',
)

app = FastAPI(
    description="test",
    root_path="/api",
    title="Livekit DB",
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
    expose_headers=["Response"],
)

@app.on_event("startup")
async def startupEvent():
    log.info("Starting MemoryDB Server.")

api_router = APIRouter()

api_router.include_router(conv)

app.include_router(conv, prefix="/v1")