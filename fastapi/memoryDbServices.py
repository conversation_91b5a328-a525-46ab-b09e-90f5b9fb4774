import json
import numpy as np
import logging

from datetime import datetime
from uuid import uuid4
from redis.commands.search.query import Query

# from sentence_transformers import SentenceTransformer

log = logging.getLogger(__name__)


def processVectors(queryResults):
    """
    This method process the vector query results.
    """
    try:
        log.info("Processing query search results")
        processedRes = []

        userQueryList = set(item.userQuery for item in queryResults)

        for item in queryResults:
            item.timestamp = datetime.strptime(item.timestamp, "%Y-%m-%d %H:%M:%S")

        # Check & handle duplicate records.
        for uq in userQueryList:
            srList = [item for item in queryResults if item.userQuery == uq]  # Find duplicate records

            if len(srList) > 1:
                latest_dict = max(srList, key=lambda
                    x: x.timestamp)  # Get the latest record from duplicate entries.
                processedRes.append(latest_dict)
            else:
                processedRes.append(srList[0])

        sortProcessRes = sorted(processedRes, key=lambda x: x.timestamp, reverse=False)  # Sort the results

        return sortProcessRes
    except Exception as vex:
        log.error("Exception arised for vector processing --> ", vex)


def vectorNormalization(queryVector):
    """
    This method normalize the vectors for the processing.
    """
    norm = np.linalg.norm(queryVector)
    return queryVector / norm if norm != 0 else queryVector


def get_query_results(mDbClient, userQuery):
    """
    This method searches for the user query results.
    """
    embedding_model = mDbClient.embededModel
    mdIdxName = mDbClient.indexName
    usrDeviceid = userQuery.get("deviceId")

    userQueryEmbed = embedding_model.encode(userQuery.get("userQuery"))

    # Unwanted bot responses list
    bot_msgs = ["Oops, I couldn't find any matching locations",
                "I wasn't able to understand your request",
                "Would you like to provide more details or try a different query?",
                "An internal error occurred"]

    query_vec_bytes = np.array(userQueryEmbed, dtype=np.float32)
    normQuery = vectorNormalization(query_vec_bytes)
    normQuery = normQuery.tobytes()

    # Vector search for memory Db
    top_k = 10
    formatted_id = str(usrDeviceid).replace("-", "\\-")

    query = Query(f"@userDeviceId:{{{formatted_id}}}=>[KNN {top_k} @vector $vec AS score]")

    searchResults = mDbClient.client.ft(f"{mdIdxName}").search(
        query,
        query_params={"vec": normQuery}
    )

    # Generate previous prompts for openai.
    searchRespList = []

    # processData = processVectors(searchResults.docs)

    if searchResults:
        for item in searchResults.docs:
            botrespx = {}

            if isinstance(item.botResponse, str):
                try:
                    btresp = json.loads(item.botResponse)

                    if isinstance(btresp, dict) and "message" in btresp:
                        botrespx = btresp

                except json.JSONDecodeError:
                    botrespx = {}
                    text = item.botResponse
                    botrespx["message"] = text

            else:
                botrespx = item.botResponse

            if botrespx.get('message') not in bot_msgs:
                if float(item.score) >= 0.30:
                    searchRespList.append({'role': 'user', 'content': item.userQuery})
                    searchRespList.append({'role': 'assistant', 'content': json.dumps(item.botResponse)})

    log.info(f"Livekit Prompts - {searchRespList}")
    return searchRespList


def saveUserQuery(mDbClient, user_dict):
    """
    This method stores the user dictionary in the user index.
    """
    embedding_model = mDbClient.get_embedding_model()
    dbId = str(uuid4())

    embedding_model = mDbClient.embededModel
    query_ebd = embedding_model.encode(user_dict.get("payload").get("userQuery"), batch_size=10)

    user_paylod = user_dict.get("payload", {})

    query_vec_bytes = np.array(query_ebd, dtype=np.float32)
    normQuery = vectorNormalization(query_vec_bytes)
    normQuery = normQuery.tobytes()

    mapping = user_paylod
    mapping['botResponse'] = json.dumps(mapping['botResponse'])
    mapping['vector'] = normQuery

    data_len = len(mapping)

    try:
        dbId = str(uuid4())
        res = mDbClient.client.hset(f"dbId:{dbId}", mapping=mapping)

        if data_len == res:
            log.info("Record saved Successfully !!!")

        return "True"

    except Exception as ex:
        log.info(f"Exception arised while saving record - {ex}")
        return "Fail"


async def processResponse(finalResponse):
    """
    This method processes the bot response.
    """
    botrespx = None
    if isinstance(finalResponse.get("assistant"), str):
        try:
            btresp = json.loads(finalResponse.get("assistant"))

            if isinstance(btresp, dict) and "message" in btresp:
                botrespx = btresp

        except json.JSONDecodeError:
            botrespx = {}
            text = finalResponse.get("assistant")
            botrespx["message"] = text

    else:
        botrespx = finalResponse

    return botrespx