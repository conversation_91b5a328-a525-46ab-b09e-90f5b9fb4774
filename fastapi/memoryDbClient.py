import logging

from redis.cluster import RedisCluster
from redis.commands.search.field import <PERSON><PERSON>ield, VectorField, TagField
from redis.commands.search.indexDefinition import IndexDefinition, IndexType

log = logging.getLogger("my-worker")


class MemoryDBClient:
    _instance = None
    client = None
    embededModel = None
    indexName = None

    def __new__(cls, *args, **kwargs):
        if cls._instance is None:
            cls._instance = super(MemoryDBClient, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        if not hasattr(self, "_initialized"):
            self._initialized = True
            self.client = None
            self.embededModel = None

    def initialize(self):
        self.clientCreation()
        self.createSearchIndex()

    def clientCreation(self):
        try:
            log.info("Memory usage before client initialization")

            self.client = RedisCluster(
                host="clustercfg.cara-memorydb.qtyizc.memorydb.ap-south-1.amazonaws.com",
                port="6379",
                ssl=True,
                ssl_cert_reqs="none",
                decode_responses=True,
            )
            self.indexName = "MdbSearchIndex"
            log.info("MemoryDb client created.")
        except Exception as e:
            log.error(f"Error initializing client: {e}")

    def createSearchIndex(self):
        """
        This method creates index for vector search.
        """
        print("\n Index creation method called !!")
        try:
            self.client.ft(f"{self.indexName}").create_index(
                fields=[
                    TagField("userDeviceId"),
                    TextField("userQuery"),
                    TextField("botResponse"),
                    TextField("latitude"),
                    TextField("longitude"),
                    TextField("type"),
                    TextField("timestamp"),
                    VectorField("vector", "HNSW", {
                        "TYPE": "FLOAT32",
                        "DIM": 768,
                        "DISTANCE_METRIC": "COSINE",
                    }),
                ],
                definition=IndexDefinition(prefix=["dbId:"], index_type=IndexType.HASH),
            )
            indexs = self.client.execute_command("FT._LIST")
            log.info(f" {self.indexName} Creation Status - {self.indexName in indexs}")
        except Exception as iex:
            log.info(f"Index Creation Error - {iex}")

    def get_embedding_model(self):
        from sentence_transformers import SentenceTransformer

        if self.embededModel is None:
            log.info("Loading embedding model...")
            self.embededModel = SentenceTransformer('all-distilroberta-v1')
        return self.embededModel
