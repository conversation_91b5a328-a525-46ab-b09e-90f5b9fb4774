from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from datetime import datetime

from memoryDbClient import MemoryD<PERSON>lient
from memoryDbServices import *

conv = APIRouter()

# Initialize MemoryDBClient globally
mDbClient = MemoryDBClient()
mDbClient.initialize()
mDbClient.get_embedding_model()


class SaveDataRequest(BaseModel):
    user_query: str
    bot_response: str
    user_device_id: str
    latitude: float = None
    longitude: float = None


class QueryDataRequest(BaseModel):
    user_query: str
    user_device_id: str


@conv.post("/save_data")
async def save_data(request: SaveDataRequest):
    try:
        user_details = {
            "payload": {
                "userQuery": request.user_query,
                "botResponse": request.bot_response,
                "userDeviceId": request.user_device_id,
                "latitude": request.latitude or 0.0,
                "longitude": request.longitude or 0.0,
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            }
        }

        saveUserQuery(mDbClient=mDbClient, user_dict=user_details)
        return {"status": "success", "message": "Data saved successfully."}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error saving data: {str(e)}")


@conv.post("/get_user_data")
async def get_user_data(request: QueryDataRequest):
    try:
        userQueryDict = {
            "deviceId": request.user_device_id,
            "userQuery": request.user_query,
        }
        query_results = get_query_results(mDbClient=mDbClient, userQuery=userQueryDict)

        if not query_results:
            return {"status": "failure", "message": "No data found."}

        return {"status": "success", "data": query_results}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching data: {str(e)}")