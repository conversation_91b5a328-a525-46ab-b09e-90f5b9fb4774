{"name": "playground", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "format:check": "prettier --check \"**/*.{ts,tsx,md,json}\"", "format:write": "prettier --write \"**/*.{ts,tsx,md,json}\""}, "dependencies": {"@hookform/resolvers": "^3.9.0", "@livekit/components-react": "^2.6.4", "@livekit/components-styles": "^1.1.2", "@livekit/krisp-noise-filter": "^0.2.12", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-tooltip": "^1.1.2", "@svgr/webpack": "^8.1.0", "@tiptap/extension-placeholder": "^2.6.6", "@tiptap/pm": "^2.6.6", "@tiptap/react": "^2.6.6", "@tiptap/starter-kit": "^2.6.6", "@react-three/drei": "^9.121.3", "@react-three/fiber": "^8.17.12", "@react-three/postprocessing": "^2.19.1", "@types/three": "^0.172.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "1.0.0", "dotenv": "^16.4.7", "framer-motion": "^11.5.4", "js-cookie": "^3.0.5", "livekit-client": "^2.6.0", "livekit-server-sdk": "^2.7.3", "lucide-react": "^0.437.0", "next": "14.2.7", "posthog-js": "^1.188.0", "postprocessing": "^6.36.6", "three": "^0.172.0", "react": "^18", "react-dom": "^18", "react-hook-form": "^7.53.0", "react-syntax-highlighter": "^15.5.0", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.1", "zod": "^3.23.8"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/react-syntax-highlighter": "^15.5.13", "@typescript-eslint/eslint-plugin": "^7.2.0", "@typescript-eslint/parser": "^7.2.0", "eslint": "^8", "eslint-config-next": "14.2.7", "eslint-config-prettier": "^8.10.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-unused-imports": "^3.0.0", "postcss": "^8", "react-syntax-highlighter": "^15.5.0", "tailwindcss": "^3.4.1", "typescript": "^5"}, "packageManager": "pnpm@9.6.0"}