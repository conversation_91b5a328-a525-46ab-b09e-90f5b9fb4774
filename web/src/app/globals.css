@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  @apply text-neutral-50 bg-neutral-900;
  --lk-va-bar-width: 2px;
  --lk-va-bar-gap: 4px;
  --lk-fg: rgb(115 115 115 / var(--tw-text-opacity));
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

@layer base {
  :root {
    --radius: 0.5rem;
  }
}

* {
  box-sizing: border-box;
}

body,
html {
  margin: 0;
  padding: 0;
  height: 100vh;
  width: 100vw;
  display: flex;
  flex-direction: column;
}

.tiptap p.is-editor-empty:first-child::before {
  color: #adb5bd;
  font-weight: 300;
  content: attr(data-placeholder);
  float: left;
  height: 0;
  pointer-events: none;
}

@font-face {
  font-family: "Commit Mono";
  src: url("/fonts/CommitMono-400-Regular.otf") format("opentype");
  font-weight: 400;
  font-style: normal;
}

@font-face {
  font-family: "Commit Mono";
  src: url("/fonts/CommitMono-700-Regular.otf") format("opentype");
  font-weight: 700;
  font-style: normal;
}
