import json
from fastapi import <PERSON><PERSON><PERSON>, HTTPException, status, Query
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel

from livekit import api

# Initialize FastAPI
app = FastAPI()

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
    expose_headers=["Response"],
)


# Define request model to accept metadata directly in the body
class MetadataRequest(BaseModel):
    model_name: str = ""
    voice: str = ""
    language: str = ""


# Define response model for token generation
class TokenResponce(BaseModel):
    token: str
    url: str


# Configuration for different environments
SANDBOX_CONFIGS = {
    "release": {
        "api_key": "API2Z4aF3Gs4KJ7",
        "api_secret": "hVdcnfJmocyd0u8wP2iaftRodZVQPXitBHdXbGFgSOA",
        "ws_url": "wss://cai-release-ehw1ezzy.livekit.cloud"
    },
    "develop": {
        "api_key": "APIjDWNBPmBRAiV",
        "api_secret": "W7wO9pCaNYYYxEUUGZejftSO6VSRmCSsSoeyZOwfFgWB",
        "ws_url": "wss://cai-development-qsjzgmj7.livekit.cloud"
    }
}


@app.post("/token", response_model=TokenResponce)
async def generate_token(
        request: MetadataRequest,
        identity: str = Query(...),
        room_name: str = Query(...),
        sandbox_id: str = Query("release", description="Sandbox environment: 'release' or 'develop'")
):
    """Generates a LiveKit access token for a user in the specified sandbox environment."""
    try:
        # Get configuration for the specified sandbox
        if sandbox_id not in SANDBOX_CONFIGS:
            raise HTTPException(
                status_code=400,
                detail=f"Invalid sandbox_id. Available options: {', '.join(SANDBOX_CONFIGS.keys())}"
            )

        config = SANDBOX_CONFIGS[sandbox_id]

        token = api.AccessToken(
            api_key=config["api_key"],
            api_secret=config["api_secret"]
        ).with_identity(identity).with_name(identity).with_metadata(
            json.dumps(request.model_dump())
        ).with_grants(
            api.VideoGrants(
                room_join=True,
                room=room_name,
                can_publish=True,
                can_publish_data=True,
                can_subscribe=True,
                can_update_own_metadata=True,
            )
        )
        return {"token": token.to_jwt(), "url": config["ws_url"]}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/health-check", status_code=status.HTTP_200_OK)
async def health_check():
    return {"message": "success"}